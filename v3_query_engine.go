package main

import (
	"fmt"
	"strings"
	"time"
)

// V3QueryEngine implements QueryEngine for UltraFast V3 format
type V3QueryEngine struct {
	indexDir string
	readers  map[string]*UltraFastV3Reader
}

// NewV3QueryEngine creates a new V3 query engine
func NewV3QueryEngine(indexDir string) *V3QueryEngine {
	return &V3QueryEngine{
		indexDir: indexDir,
		readers:  make(map[string]*UltraFastV3Reader),
	}
}

// GetApproach returns the indexing approach identifier
func (e *V3QueryEngine) GetApproach() IndexingApproach {
	return ApproachV3
}

// Initialize prepares the query engine with index directory
func (e *V3QueryEngine) Initialize(indexDir string) error {
	if indexDir != "" {
		e.indexDir = indexDir
	}
	return nil
}

// ExecuteQuery executes a single filter query
func (e *V3QueryEngine) ExecuteQuery(columnName string, filter QueryFilter) (*QueryResult, error) {
	start := time.Now()

	// Get or create reader for this column
	reader, err := e.getReader(columnName)
	if err != nil {
		return nil, err
	}

	// Convert filter to V3 format
	v3Filters := map[string]interface{}{
		columnName: filter.Value,
	}

	// Execute query
	results, err := reader.Query(v3Filters)
	if err != nil {
		return nil, err
	}

	// Convert results to line numbers
	// Note: This is a simplified implementation
	// In a real V3 implementation, we would need to track line numbers properly
	lineNumbers := make([]uint32, len(results))
	for i := range results {
		lineNumbers[i] = uint32(i + 1) // Simplified line number assignment
	}

	return &QueryResult{
		LineNumbers:   lineNumbers,
		ExecutionTime: time.Since(start),
		ResultCount:   len(lineNumbers),
		IndexHits:     1,
	}, nil
}

// ExecuteMultiColumnQuery executes a query with multiple filters
func (e *V3QueryEngine) ExecuteMultiColumnQuery(filters []QueryFilter) (*QueryResult, error) {
	if len(filters) == 0 {
		return &QueryResult{}, nil
	}

	start := time.Now()

	// For multi-column queries, we need to find a file that contains all columns
	// This is a simplified implementation that assumes all columns are in the same file
	tableName := e.inferTableName(filters)
	reader, err := e.getTableReader(tableName)
	if err != nil {
		return nil, err
	}

	// Convert filters to V3 format
	v3Filters := make(map[string]interface{})
	for _, filter := range filters {
		v3Filters[filter.Column] = filter.Value
	}

	// Execute query
	results, err := reader.Query(v3Filters)
	if err != nil {
		return nil, err
	}

	// Convert results to line numbers
	lineNumbers := make([]uint32, len(results))
	for i := range results {
		lineNumbers[i] = uint32(i + 1) // Simplified line number assignment
	}

	return &QueryResult{
		LineNumbers:   lineNumbers,
		ExecutionTime: time.Since(start),
		ResultCount:   len(lineNumbers),
		IndexHits:     len(filters),
	}, nil
}

// ExecuteBatchQueries executes multiple queries and returns aggregated results
func (e *V3QueryEngine) ExecuteBatchQueries(queries []QueryFilter) ([]*QueryResult, error) {
	results := make([]*QueryResult, len(queries))

	for i, query := range queries {
		result, err := e.ExecuteQuery(query.Column, query)
		if err != nil {
			return nil, fmt.Errorf("batch query %d failed: %v", i, err)
		}
		results[i] = result
	}

	return results, nil
}

// GetSupportedOperators returns the list of supported query operators
func (e *V3QueryEngine) GetSupportedOperators() []string {
	return []string{"=", "!=", ">", "<", ">=", "<=", "IN", "LIKE"}
}

// Close releases any resources held by the query engine
func (e *V3QueryEngine) Close() error {
	for _, reader := range e.readers {
		if err := reader.Close(); err != nil {
			return err
		}
	}
	e.readers = make(map[string]*UltraFastV3Reader)
	return nil
}

// Helper methods

// getReader gets or creates a reader for a specific column
func (e *V3QueryEngine) getReader(columnName string) (*UltraFastV3Reader, error) {
	if reader, exists := e.readers[columnName]; exists {
		return reader, nil
	}

	filename := fmt.Sprintf("%s/%s_ultrafast_v3.uf3", e.indexDir, columnName)
	reader, err := NewUltraFastV3Reader(filename)
	if err != nil {
		return nil, err
	}

	e.readers[columnName] = reader
	return reader, nil
}

// getTableReader gets or creates a reader for a table (multi-column file)
func (e *V3QueryEngine) getTableReader(tableName string) (*UltraFastV3Reader, error) {
	if reader, exists := e.readers[tableName]; exists {
		return reader, nil
	}

	filename := fmt.Sprintf("%s/%s_ultrafast_v3.uf3", e.indexDir, tableName)
	reader, err := NewUltraFastV3Reader(filename)
	if err != nil {
		return nil, err
	}

	e.readers[tableName] = reader
	return reader, nil
}

// inferTableName tries to infer the table name from filters
func (e *V3QueryEngine) inferTableName(filters []QueryFilter) string {
	// This is a simplified heuristic
	// In a real implementation, you would have metadata about which columns belong to which tables
	
	if len(filters) == 0 {
		return "default_table"
	}

	// Try to find a common prefix or use a default naming convention
	columnNames := make([]string, len(filters))
	for i, filter := range filters {
		columnNames[i] = filter.Column
	}

	// For now, just use a default table name
	// In a real implementation, you would have a catalog or metadata store
	return "demo_table"
}

// intersectResults finds the intersection of multiple result sets
func (e *V3QueryEngine) intersectResults(results [][]map[string]interface{}) []map[string]interface{} {
	if len(results) == 0 {
		return []map[string]interface{}{}
	}

	if len(results) == 1 {
		return results[0]
	}

	// This is a simplified intersection based on row position
	// In a real implementation, you would need proper row IDs or keys
	minLen := len(results[0])
	for _, result := range results[1:] {
		if len(result) < minLen {
			minLen = len(result)
		}
	}

	intersection := make([]map[string]interface{}, 0, minLen)
	for i := 0; i < minLen; i++ {
		// Simplified: assume all results at the same index represent the same row
		// In reality, you would need to match on row keys or IDs
		row := make(map[string]interface{})
		for _, result := range results {
			if i < len(result) {
				for k, v := range result[i] {
					row[k] = v
				}
			}
		}
		intersection = append(intersection, row)
	}

	return intersection
}

// formatColumnName formats column names for file lookup
func (e *V3QueryEngine) formatColumnName(columnName string) string {
	// Convert to lowercase and replace spaces with underscores
	return strings.ToLower(strings.ReplaceAll(columnName, " ", "_"))
}
