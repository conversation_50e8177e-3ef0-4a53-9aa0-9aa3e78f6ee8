package main

import (
	"encoding/csv"
	"fmt"
	"math/rand"
	"os"
	"strconv"
	"strings"
	"time"
)

// TestDataGenerator implements the DataGenerator interface
type TestDataGenerator struct {
	seed int64
}

// NewTestDataGenerator creates a new data generator
func NewTestDataGenerator() *TestDataGenerator {
	return &TestDataGenerator{
		seed: time.Now().UnixNano(),
	}
}

// SetSeed sets the random seed for reproducible data generation
func (tdg *TestDataGenerator) SetSeed(seed int64) {
	tdg.seed = seed
}

// GenerateRealisticData creates realistic test data with various patterns
func (tdg *TestDataGenerator) GenerateRealisticData(numRows int, schema map[string]string) (TestDataset, error) {
	rand.Seed(tdg.seed)

	dataset := TestDataset{
		Name:        fmt.Sprintf("synthetic_%d", numRows),
		Size:        numRows,
		ColumnData:  make(map[string][]Record),
		Description: fmt.Sprintf("Synthetic dataset with %d rows", numRows),
	}

	// Define realistic value distributions
	distributions := tdg.getValueDistributions()

	// Generate data for each column
	for columnName, columnType := range schema {
		dataset.ColumnData[columnName] = tdg.generateColumnData(columnName, columnType, numRows, distributions)
	}

	// Generate query set
	querySet, err := tdg.GenerateQuerySet(dataset, 10)
	if err == nil {
		dataset.QuerySet = querySet
	}

	return dataset, nil
}

// getValueDistributions returns predefined value distributions for realistic data
func (tdg *TestDataGenerator) getValueDistributions() map[string][]string {
	return map[string][]string{
		"protocol":        {"TCP", "UDP", "ICMP", "HTTP", "HTTPS", "FTP", "SSH", "DNS", "SMTP", "POP3"},
		"rule_name":       {"ALLOW_WEB", "BLOCK_MALWARE", "ALLOW_SSH", "BLOCK_P2P", "ALLOW_DNS", "BLOCK_SPAM", "ALLOW_FTP", "BLOCK_INTRUSION"},
		"action":          {"ALLOW", "BLOCK", "LOG", "ALERT"},
		"severity":        {"LOW", "MEDIUM", "HIGH", "CRITICAL"},
		"country":         {"US", "UK", "DE", "FR", "JP", "CN", "IN", "BR", "CA", "AU", "RU", "IT", "ES", "NL", "SE"},
		"source_username": {"john_doe", "admin", "guest", "system", "root", "alice", "bob", "charlie", "dave", "eve"},
		"dest_username":   {"admin", "system", "www-data", "nobody", "guest", "root", "service", "daemon", "user"},
	}
}

// generateColumnData creates data for a specific column
func (tdg *TestDataGenerator) generateColumnData(columnName, columnType string, numRows int, distributions map[string][]string) []Record {
	records := make([]Record, numRows)

	// Check if we have a predefined distribution
	if values, exists := distributions[columnName]; exists {
		// Use predefined distribution with different patterns
		switch columnName {
		case "protocol":
			// Low cardinality, 80% TCP/UDP
			for i := 0; i < numRows; i++ {
				records[i].LineNumber = uint32(i + 1)
				if rand.Float32() < 0.8 {
					records[i].Value = []string{"TCP", "UDP"}[rand.Intn(2)]
				} else {
					records[i].Value = values[rand.Intn(len(values))]
				}
			}
		case "action":
			// Very low cardinality, 70% ALLOW
			for i := 0; i < numRows; i++ {
				records[i].LineNumber = uint32(i + 1)
				if rand.Float32() < 0.7 {
					records[i].Value = "ALLOW"
				} else {
					records[i].Value = values[rand.Intn(len(values))]
				}
			}
		default:
			// Medium cardinality
			for i := 0; i < numRows; i++ {
				records[i].LineNumber = uint32(i + 1)
				records[i].Value = values[rand.Intn(len(values))]
			}
		}
	} else {
		// Generate based on column type
		switch columnType {
		case "string":
			for i := 0; i < numRows; i++ {
				records[i].LineNumber = uint32(i + 1)
				records[i].Value = tdg.generateRandomString(8 + rand.Intn(8))
			}
		case "int":
			for i := 0; i < numRows; i++ {
				records[i].LineNumber = uint32(i + 1)
				records[i].Value = strconv.Itoa(rand.Intn(10000))
			}
		case "ip":
			for i := 0; i < numRows; i++ {
				records[i].LineNumber = uint32(i + 1)
				records[i].Value = tdg.generateRandomIP()
			}
		case "timestamp":
			baseTime := time.Now().Add(-24 * time.Hour)
			for i := 0; i < numRows; i++ {
				records[i].LineNumber = uint32(i + 1)
				jitter := time.Duration(rand.Intn(1000)) * time.Millisecond
				t := baseTime.Add(time.Duration(i)*time.Second + jitter)
				records[i].Value = t.Format(time.RFC3339)
			}
		default:
			// Default to random strings
			for i := 0; i < numRows; i++ {
				records[i].LineNumber = uint32(i + 1)
				records[i].Value = tdg.generateRandomString(8 + rand.Intn(8))
			}
		}
	}

	return records
}

// generateRandomString creates a random string of specified length
func (tdg *TestDataGenerator) generateRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
}

// generateRandomIP creates a random IP address
func (tdg *TestDataGenerator) generateRandomIP() string {
	if rand.Float32() < 0.6 {
		// Internal networks
		return fmt.Sprintf("192.168.%d.%d", rand.Intn(10), rand.Intn(256))
	} else {
		// External IPs
		return fmt.Sprintf("%d.%d.%d.%d",
			rand.Intn(256), rand.Intn(256), rand.Intn(256), rand.Intn(256))
	}
}

// GenerateQuerySet creates a set of queries for testing
func (tdg *TestDataGenerator) GenerateQuerySet(dataset TestDataset, numQueries int) ([]QueryFilter, error) {
	if len(dataset.ColumnData) == 0 {
		return nil, fmt.Errorf("dataset has no columns")
	}

	queries := make([]QueryFilter, 0, numQueries)
	columns := make([]string, 0, len(dataset.ColumnData))
	
	for column := range dataset.ColumnData {
		columns = append(columns, column)
	}

	// Generate equality queries
	for i := 0; i < numQueries; i++ {
		column := columns[rand.Intn(len(columns))]
		columnData := dataset.ColumnData[column]
		
		if len(columnData) == 0 {
			continue
		}
		
		// Pick a random value from the column
		valueIndex := rand.Intn(len(columnData))
		value := columnData[valueIndex].Value
		
		queries = append(queries, QueryFilter{
			Column:   column,
			Operator: "=",
			Value:    value,
		})
	}

	return queries, nil
}

// LoadFromCSV loads data from a CSV file
func (tdg *TestDataGenerator) LoadFromCSV(filename string) (TestDataset, error) {
	file, err := os.Open(filename)
	if err != nil {
		return TestDataset{}, err
	}
	defer file.Close()

	reader := csv.NewReader(file)
	records, err := reader.ReadAll()
	if err != nil {
		return TestDataset{}, err
	}

	if len(records) < 2 {
		return TestDataset{}, fmt.Errorf("CSV file must have at least a header row and one data row")
	}

	headers := records[0]
	dataset := TestDataset{
		Name:        strings.TrimSuffix(filename, ".csv"),
		Size:        len(records) - 1,
		ColumnData:  make(map[string][]Record),
		Description: fmt.Sprintf("Dataset loaded from %s with %d rows", filename, len(records)-1),
	}

	// Process each column
	for colIndex, columnName := range headers {
		columnRecords := make([]Record, 0, len(records)-1)
		
		for rowIndex := 1; rowIndex < len(records); rowIndex++ {
			if colIndex < len(records[rowIndex]) && records[rowIndex][colIndex] != "" {
				columnRecords = append(columnRecords, Record{
					LineNumber: uint32(rowIndex),
					Value:      records[rowIndex][colIndex],
				})
			}
		}
		
		dataset.ColumnData[columnName] = columnRecords
	}

	// Generate query set
	querySet, err := tdg.GenerateQuerySet(dataset, 10)
	if err == nil {
		dataset.QuerySet = querySet
	}

	return dataset, nil
}

// SaveToCSV saves a dataset to a CSV file
func (tdg *TestDataGenerator) SaveToCSV(dataset TestDataset, filename string) error {
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	writer := csv.NewWriter(file)
	defer writer.Flush()

	// Get all column names
	columns := make([]string, 0, len(dataset.ColumnData))
	for column := range dataset.ColumnData {
		columns = append(columns, column)
	}

	// Write header
	if err := writer.Write(columns); err != nil {
		return err
	}

	// Create a map of line numbers to rows
	rowMap := make(map[uint32][]string)
	maxLineNumber := uint32(0)

	for colIndex, column := range columns {
		columnData := dataset.ColumnData[column]
		for _, record := range columnData {
			if record.LineNumber > maxLineNumber {
				maxLineNumber = record.LineNumber
			}
			
			if _, exists := rowMap[record.LineNumber]; !exists {
				rowMap[record.LineNumber] = make([]string, len(columns))
			}
			
			rowMap[record.LineNumber][colIndex] = record.Value
		}
	}

	// Write rows in order
	for i := uint32(1); i <= maxLineNumber; i++ {
		if row, exists := rowMap[i]; exists {
			if err := writer.Write(row); err != nil {
				return err
			}
		}
	}

	return nil
}
