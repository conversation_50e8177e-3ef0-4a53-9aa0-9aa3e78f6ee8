package main

import (
	"fmt"
	"os"
	"time"
)

// V3Generator implements IndexGenerator for UltraFast V3 format
type V3Generator struct {
	outputDir string
}

// NewV3Generator creates a new V3 format generator
func NewV3Generator(outputDir string) *V3Generator {
	return &V3Generator{outputDir: outputDir}
}

// GetApproach returns the indexing approach identifier
func (g *V3Generator) GetApproach() IndexingApproach {
	return ApproachV3
}

// GetFeatures returns a list of features supported by this approach
func (g *V3Generator) GetFeatures() []string {
	return []string{
		"Columnar Storage",
		"Dictionary Encoding",
		"Run Length Encoding",
		"Delta Encoding",
		"Frame of Reference",
		"Bit Packing",
		"Zone Maps",
		"Bloom Filters",
		"Block-based Storage",
		"Schema Evolution",
		"Multi-level Indexing",
		"Adaptive Compression",
	}
}

// GenerateIndex creates an index for the given column data
func (g *V3Generator) GenerateIndex(columnName string, records []Record, outputDir string) (*IndexStats, error) {
	if outputDir != "" {
		g.outputDir = outputDir
	}

	start := time.Now()

	// Create output directory
	if err := os.MkdirAll(g.outputDir, 0755); err != nil {
		return nil, err
	}

	filename := fmt.Sprintf("%s/%s_ultrafast_v3.uf3", g.outputDir, columnName)

	// Convert records to interface{} format for V3 writer
	data := make([]interface{}, len(records))
	for i, record := range records {
		data[i] = record.Value
	}

	// Create V3 writer
	writer, err := NewUltraFastV3Writer(filename)
	if err != nil {
		return nil, err
	}
	defer writer.Close()

	// Add column definition (assume string type for now)
	writer.AddColumn(columnName, TYPE_STRING)

	// Prepare column data in the format expected by V3 writer
	columnData := [][]interface{}{data}

	// Write data
	if err := writer.WriteData(columnData); err != nil {
		return nil, err
	}

	buildTime := time.Since(start)

	// Get file stats
	fileInfo, err := os.Stat(filename)
	if err != nil {
		return nil, err
	}

	// Calculate unique values
	uniqueValues := make(map[string]bool)
	for _, record := range records {
		uniqueValues[record.Value] = true
	}

	return &IndexStats{
		Approach:         ApproachV3,
		ColumnName:       columnName,
		RecordCount:      uint32(len(records)),
		UniqueValues:     uint32(len(uniqueValues)),
		FileSize:         fileInfo.Size(),
		CompressionRatio: g.calculateCompressionRatio(records, fileInfo.Size()),
		BuildTime:        buildTime,
		Features:         g.GetFeatures(),
	}, nil
}

// GenerateMultiColumnIndex creates indexes for multiple columns
func (g *V3Generator) GenerateMultiColumnIndex(columnData map[string][]Record, outputDir string, tableName string) (map[string]*IndexStats, error) {
	if outputDir != "" {
		g.outputDir = outputDir
	}

	start := time.Now()

	// Create output directory
	if err := os.MkdirAll(g.outputDir, 0755); err != nil {
		return nil, err
	}

	filename := fmt.Sprintf("%s/%s_ultrafast_v3.uf3", g.outputDir, tableName)

	// Create V3 writer
	writer, err := NewUltraFastV3Writer(filename)
	if err != nil {
		return nil, err
	}
	defer writer.Close()

	// Determine the maximum number of rows
	maxRows := 0
	for _, records := range columnData {
		if len(records) > maxRows {
			maxRows = len(records)
		}
	}

	// Add column definitions
	columnNames := make([]string, 0, len(columnData))
	for columnName := range columnData {
		columnNames = append(columnNames, columnName)
		writer.AddColumn(columnName, TYPE_STRING) // Assume string type for now
	}

	// Prepare data in row-major format for V3 writer
	v3ColumnData := make([][]interface{}, len(columnNames))
	for i, columnName := range columnNames {
		records := columnData[columnName]
		v3ColumnData[i] = make([]interface{}, maxRows)
		
		// Create a map for quick lookup
		recordMap := make(map[uint32]string)
		for _, record := range records {
			recordMap[record.LineNumber] = record.Value
		}
		
		// Fill the column data
		for row := 0; row < maxRows; row++ {
			lineNumber := uint32(row + 1)
			if value, exists := recordMap[lineNumber]; exists {
				v3ColumnData[i][row] = value
			} else {
				v3ColumnData[i][row] = "" // Empty value for missing data
			}
		}
	}

	// Write data
	if err := writer.WriteData(v3ColumnData); err != nil {
		return nil, err
	}

	buildTime := time.Since(start)

	// Get file stats
	fileInfo, err := os.Stat(filename)
	if err != nil {
		return nil, err
	}

	// Create stats for each column
	stats := make(map[string]*IndexStats)
	for _, columnName := range columnNames {
		records := columnData[columnName]
		
		// Calculate unique values
		uniqueValues := make(map[string]bool)
		for _, record := range records {
			uniqueValues[record.Value] = true
		}

		stats[columnName] = &IndexStats{
			Approach:         ApproachV3,
			ColumnName:       columnName,
			RecordCount:      uint32(len(records)),
			UniqueValues:     uint32(len(uniqueValues)),
			FileSize:         fileInfo.Size() / int64(len(columnNames)), // Approximate per-column size
			CompressionRatio: g.calculateCompressionRatio(records, fileInfo.Size()/int64(len(columnNames))),
			BuildTime:        buildTime / time.Duration(len(columnNames)), // Approximate per-column time
			Features:         g.GetFeatures(),
		}
	}

	return stats, nil
}

// ValidateIndex validates the integrity of an index file
func (g *V3Generator) ValidateIndex(indexPath string) error {
	reader, err := NewUltraFastV3Reader(indexPath)
	if err != nil {
		return err
	}
	defer reader.Close()

	// Basic validation - if we can read the header and metadata, it's probably valid
	stats := reader.GetStats()
	if stats["version"] == nil {
		return fmt.Errorf("invalid V3 file: missing version information")
	}

	return nil
}

// GetIndexStats returns statistics about an existing index
func (g *V3Generator) GetIndexStats(indexPath string) (*IndexStats, error) {
	reader, err := NewUltraFastV3Reader(indexPath)
	if err != nil {
		return nil, err
	}
	defer reader.Close()

	stats := reader.GetStats()
	
	// Get file size
	fileInfo, err := os.Stat(indexPath)
	if err != nil {
		return nil, err
	}

	return &IndexStats{
		Approach:     ApproachV3,
		RecordCount:  uint32(stats["num_rows"].(uint64)),
		FileSize:     fileInfo.Size(),
		Features:     g.GetFeatures(),
	}, nil
}

// calculateCompressionRatio estimates compression ratio
func (g *V3Generator) calculateCompressionRatio(records []Record, indexSize int64) float64 {
	// Estimate raw data size
	estimatedRawSize := int64(0)
	for _, record := range records {
		estimatedRawSize += int64(len(record.Value)) + 8 // value + line number
	}

	if estimatedRawSize > 0 {
		return 1.0 - float64(indexSize)/float64(estimatedRawSize)
	}

	return 0.0
}
