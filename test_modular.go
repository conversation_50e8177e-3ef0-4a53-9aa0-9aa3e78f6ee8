package main

import (
	"fmt"
	"os"
	"testing"
	"time"
)

// TestModularSystem tests the basic functionality of the modular system
func TestModularSystem() {
	fmt.Println("🧪 Testing Modular Indexing System")
	fmt.Println("==================================")

	// Initialize registry
	InitializeRegistry()

	// Test 1: Registry functionality
	fmt.Println("\n1. Testing Registry...")
	implementations := GetAllImplementations()
	if len(implementations) == 0 {
		fmt.Println("❌ No implementations registered")
		return
	}
	fmt.Printf("✅ Found %d implementations\n", len(implementations))

	for _, impl := range implementations {
		fmt.Printf("   - %s (%s)\n", impl.Name, impl.Generator.GetApproach())
	}

	// Test 2: Data generation
	fmt.Println("\n2. Testing Data Generation...")
	generator := NewTestDataGenerator()
	schema := map[string]string{
		"protocol": "string",
		"action":   "string",
		"port":     "int",
	}

	dataset, err := generator.GenerateRealisticData(100, schema)
	if err != nil {
		fmt.Printf("❌ Data generation failed: %v\n", err)
		return
	}
	fmt.Printf("✅ Generated %d records with %d columns\n", dataset.Size, len(dataset.ColumnData))

	// Test 3: Test each implementation
	for _, impl := range implementations {
		fmt.Printf("\n3. Testing %s...\n", impl.Name)
		
		outputDir := fmt.Sprintf("./test_temp/%s", impl.Generator.GetApproach())
		os.RemoveAll(outputDir)
		defer os.RemoveAll(outputDir)

		// Test index generation
		start := time.Now()
		stats, err := impl.Generator.GenerateMultiColumnIndex(dataset.ColumnData, outputDir, "test_table")
		if err != nil {
			fmt.Printf("❌ Index generation failed: %v\n", err)
			continue
		}
		genTime := time.Since(start)
		fmt.Printf("   ✅ Index generation: %v\n", genTime)

		// Test query engine
		if err := impl.QueryEngine.Initialize(outputDir); err != nil {
			fmt.Printf("❌ Query engine initialization failed: %v\n", err)
			continue
		}

		// Test a simple query
		if len(dataset.QuerySet) > 0 {
			query := dataset.QuerySet[0]
			start = time.Now()
			result, err := impl.QueryEngine.ExecuteQuery(query.Column, query)
			queryTime := time.Since(start)
			
			if err != nil {
				fmt.Printf("❌ Query failed: %v\n", err)
			} else {
				fmt.Printf("   ✅ Query execution: %v (%d results)\n", queryTime, result.ResultCount)
			}
		}

		impl.QueryEngine.Close()
	}

	// Test 4: Benchmark framework
	fmt.Println("\n4. Testing Benchmark Framework...")
	benchmark := NewComprehensiveBenchmark()
	
	// Run a quick benchmark with limited data
	smallDataset := TestDataset{
		Name:       "test_small",
		Size:       50,
		ColumnData: make(map[string][]Record),
	}
	
	// Create small dataset
	for columnName, records := range dataset.ColumnData {
		if len(records) > 50 {
			smallDataset.ColumnData[columnName] = records[:50]
		} else {
			smallDataset.ColumnData[columnName] = records
		}
	}
	smallDataset.QuerySet = dataset.QuerySet[:1] // Just one query

	results, err := benchmark.RunComparison(implementations, smallDataset)
	if err != nil {
		fmt.Printf("❌ Benchmark failed: %v\n", err)
		return
	}

	fmt.Printf("✅ Benchmark completed with %d results\n", len(results))

	// Generate report
	report, err := benchmark.GenerateReport(results)
	if err != nil {
		fmt.Printf("❌ Report generation failed: %v\n", err)
		return
	}

	fmt.Println("\n📊 Benchmark Report:")
	fmt.Println(report)

	fmt.Println("\n🎉 All tests completed successfully!")
}

// RunBasicTest runs a basic functionality test
func RunBasicTest() {
	fmt.Println("🔧 Running Basic Functionality Test")
	fmt.Println("===================================")

	// Initialize
	InitializeRegistry()

	// Test V2 implementation
	fmt.Println("\nTesting V2 Implementation:")
	v2Impl, err := GetImplementation(ApproachV2)
	if err != nil {
		fmt.Printf("❌ Error: %v\n", err)
		return
	}

	fmt.Printf("✅ %s loaded successfully\n", v2Impl.Name)
	fmt.Printf("   Features: %d\n", len(v2Impl.Generator.GetFeatures()))
	fmt.Printf("   Operators: %v\n", v2Impl.QueryEngine.GetSupportedOperators())

	// Test V3 implementation
	fmt.Println("\nTesting V3 Implementation:")
	v3Impl, err := GetImplementation(ApproachV3)
	if err != nil {
		fmt.Printf("❌ Error: %v\n", err)
		return
	}

	fmt.Printf("✅ %s loaded successfully\n", v3Impl.Name)
	fmt.Printf("   Features: %d\n", len(v3Impl.Generator.GetFeatures()))
	fmt.Printf("   Operators: %v\n", v3Impl.QueryEngine.GetSupportedOperators())

	fmt.Println("\n✅ Basic test completed!")
}

// TestWithRealData tests with the actual mock_data.csv if available
func TestWithRealData() {
	fmt.Println("📁 Testing with Real Data (mock_data.csv)")
	fmt.Println("==========================================")

	// Check if mock_data.csv exists
	if _, err := os.Stat("mock_data.csv"); os.IsNotExist(err) {
		fmt.Println("❌ mock_data.csv not found, skipping real data test")
		return
	}

	// Initialize
	InitializeRegistry()

	// Load real data
	generator := NewTestDataGenerator()
	dataset, err := generator.LoadFromCSV("mock_data.csv")
	if err != nil {
		fmt.Printf("❌ Error loading CSV: %v\n", err)
		return
	}

	fmt.Printf("✅ Loaded %d records with %d columns\n", dataset.Size, len(dataset.ColumnData))

	// Limit dataset size for testing
	if dataset.Size > 1000 {
		for columnName, records := range dataset.ColumnData {
			if len(records) > 1000 {
				dataset.ColumnData[columnName] = records[:1000]
			}
		}
		dataset.Size = 1000
		fmt.Printf("   Limited to %d records for testing\n", dataset.Size)
	}

	// Test with V2 implementation
	fmt.Println("\nTesting V2 with real data...")
	v2Impl, _ := GetImplementation(ApproachV2)
	
	outputDir := "./test_real_v2"
	os.RemoveAll(outputDir)
	defer os.RemoveAll(outputDir)

	start := time.Now()
	stats, err := v2Impl.Generator.GenerateMultiColumnIndex(dataset.ColumnData, outputDir, "real_test")
	if err != nil {
		fmt.Printf("❌ V2 generation failed: %v\n", err)
	} else {
		fmt.Printf("✅ V2 generation completed in %v\n", time.Since(start))
		
		totalSize := int64(0)
		for _, stat := range stats {
			totalSize += stat.FileSize
		}
		fmt.Printf("   Total index size: %s\n", formatBytes(totalSize))
	}

	fmt.Println("\n✅ Real data test completed!")
}

// main function for running tests
func main() {
	if len(os.Args) > 1 {
		switch os.Args[1] {
		case "test":
			TestModularSystem()
		case "basic":
			RunBasicTest()
		case "real":
			TestWithRealData()
		case "demo":
			runDemo()
		default:
			// Run the new main
			demoMain()
		}
	} else {
		// Default to basic test
		RunBasicTest()
	}
}
