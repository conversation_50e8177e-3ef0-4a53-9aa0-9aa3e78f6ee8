package main

import (
	"fmt"
	"log"
	"os"
	"time"
)

func main() {
	fmt.Println("🧪 UltraFast V3 Basic Test")
	fmt.Println("Testing core functionality...")

	// Create test data
	testData := [][]interface{}{
		// protocol column (strings)
		{"TCP", "UDP", "TCP", "HTTP", "HTTPS", "TCP", "UDP", "HTTP"},
		// rule_name column (strings)  
		{"ALLOW_WEB", "BLOCK_MALWARE", "ALLOW_WEB", "ALLOW_WEB", "ALLOW_WEB", "BLOCK_MALWARE", "ALLOW_DNS", "ALLOW_WEB"},
		// port column (integers)
		{int32(80), int32(53), int32(443), int32(80), int32(443), int32(22), int32(53), int32(8080)},
		// timestamp column (monotonic)
		{
			time.Now(),
			time.Now().Add(1 * time.Second),
			time.Now().Add(2 * time.Second),
			time.Now().Add(3 * time.Second),
			time.Now().Add(4 * time.Second),
			time.Now().Add(5 * time.Second),
			time.Now().Add(6 * time.Second),
			time.Now().Add(7 * time.Second),
		},
		// bytes column (wide range)
		{int64(1024), int64(512), int64(2048), int64(4096), int64(8192), int64(256), int64(1536), int64(3072)},
		// action column (low cardinality)
		{"ALLOW", "BLOCK", "ALLOW", "ALLOW", "ALLOW", "BLOCK", "ALLOW", "ALLOW"},
	}

	columnNames := []string{"protocol", "rule_name", "port", "timestamp", "bytes_transferred", "action"}
	columnTypes := []uint8{TYPE_STRING, TYPE_STRING, TYPE_INT32, TYPE_TIMESTAMP, TYPE_INT64, TYPE_STRING}

	filename := "test_v3_basic.uf3"
	defer os.Remove(filename)

	// Test writing
	fmt.Print("Writing V3 file... ")
	start := time.Now()

	writer, err := NewUltraFastV3Writer(filename)
	if err != nil {
		log.Fatalf("Failed to create writer: %v", err)
	}

	// Add columns
	for i, name := range columnNames {
		writer.AddColumn(name, columnTypes[i])
	}

	// Write data
	err = writer.WriteData(testData)
	if err != nil {
		log.Fatalf("Failed to write data: %v", err)
	}

	writer.Close()
	writeTime := time.Since(start)
	fmt.Printf("✅ Complete (%v)\n", writeTime)

	// Test reading
	fmt.Print("Reading V3 file... ")
	start = time.Now()

	reader, err := NewUltraFastV3Reader(filename)
	if err != nil {
		log.Fatalf("Failed to create reader: %v", err)
	}
	defer reader.Close()

	readTime := time.Since(start)
	fmt.Printf("✅ Complete (%v)\n", readTime)

	// Display file stats
	fmt.Println("\n📊 File Statistics:")
	stats := reader.GetStats()
	for key, value := range stats {
		if key != "columns" {
			fmt.Printf("  %s: %v\n", key, value)
		}
	}

	// Display column information
	fmt.Println("\n📋 Column Information:")
	if columns, ok := stats["columns"].([]map[string]interface{}); ok {
		for _, col := range columns {
			fmt.Printf("  %s: type=%v, encoding=%v, cardinality=%v\n", 
				col["name"], col["type"], col["encoding"], col["cardinality"])
		}
	}

	// Test query
	fmt.Print("\nTesting query... ")
	start = time.Now()
	
	filters := map[string]interface{}{
		"protocol": "TCP",
		"action":   "ALLOW",
	}
	
	results, err := reader.Query(filters)
	if err != nil {
		log.Printf("Query failed: %v", err)
	} else {
		queryTime := time.Since(start)
		fmt.Printf("✅ Complete (%v)\n", queryTime)
		fmt.Printf("  Found %d results\n", len(results))
	}

	// Get file size
	fileInfo, err := os.Stat(filename)
	if err == nil {
		fmt.Printf("\n💾 File size: %d bytes\n", fileInfo.Size())
		
		// Estimate compression ratio
		rawSize := int64(len(testData[0])) * int64(len(testData)) * 50 // Rough estimate
		compressionRatio := 1.0 - float64(fileInfo.Size())/float64(rawSize)
		fmt.Printf("📦 Estimated compression: %.1f%%\n", compressionRatio*100)
	}

	fmt.Println("\n✅ Basic test completed successfully!")
	fmt.Println("\n🎯 Key Features Demonstrated:")
	fmt.Println("  • Dictionary encoding for low-cardinality strings")
	fmt.Println("  • Delta encoding for monotonic timestamps")
	fmt.Println("  • Adaptive compression selection")
	fmt.Println("  • Columnar storage with metadata")
	fmt.Println("  • Fast query capabilities")
}
