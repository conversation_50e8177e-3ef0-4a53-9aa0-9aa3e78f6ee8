# UltraFast Performance Comparison Tools

This directory contains tools for comparing the performance of different UltraFast indexing implementations.

## 🚀 Available Tools

### 1. <PERSON><PERSON> (recommended)
```bash
./compare.sh [command]
```

### 2. <PERSON>ript (alternative)
```bash
python3 run_comparison.py [command]
```

### 3. Go Demo
```bash
go run comparison_demo.go [command]
```

## 📊 Available Commands

| Command | Description |
|---------|-------------|
| `demo` | Run simulated performance comparison demo |
| `real` | Run real performance comparison with existing implementations |
| `v2` | Test UltraFast V2 implementation only |
| `quick` | Run quick benchmark with V2 |
| `help` | Show help message |

## 🔧 Examples

### Run the Demo Comparison
```bash
./compare.sh demo
```
This shows a simulated comparison of multiple indexing approaches with realistic performance metrics.

### Run a Quick V2 Benchmark
```bash
./compare.sh quick
```
This runs a quick benchmark of the V2 implementation with your actual data.

### Test V2 Implementation
```bash
./compare.sh v2
```
This tests the V2 implementation with more detailed metrics.

### Run Real Comparison
```bash
./compare.sh real
```
This runs a real comparison between available implementations.

## 📋 Prerequisites

- Go installed and available in PATH
- `mock_data.csv` (will be created if missing)
- `main.go`, `ultrafast_v2.go` (for real tests)

## 📈 Sample Output

```
📈 Performance Summary:
--------------------------------------------------------------------------------
Implementation  Build Time   Query Time   QPS        Size       Compression
--------------------------------------------------------------------------------
UltraFast V2    245ms        487µs        2053       1.2MB      67.8    %
UltraFast V3    892ms        1.2ms        833        856KB      78.2    %
B-Tree Index    156ms        2.3ms        435        2.1MB      45.1    %
LSM Tree        1.2s         890µs        1124       743KB      82.3    %

🏆 Winners by Category:
  🚀 Fastest Build: B-Tree Index (156ms)
  ⚡ Fastest Query: UltraFast V2 (487μs)
  📦 Best Compression: LSM Tree (82.3%)
  💾 Smallest Size: LSM Tree (743KB)
```

## 🔍 How It Works

### Demo Mode
The demo mode simulates a comparison between multiple indexing approaches with realistic performance metrics. This is useful for visualizing how the comparison framework works without requiring actual implementations.

### Quick Mode
The quick mode runs a simple benchmark of the V2 implementation with your actual data. It measures:
- Index generation time
- Query execution time
- Index size
- Result count

### Real Mode
The real mode runs a comprehensive comparison between all available implementations. It requires that all implementations be properly set up and available.

### V2 Mode
The V2 mode tests only the V2 implementation with detailed metrics.

## 🛠️ Customizing

### Adding New Implementations
To add a new implementation to the comparison:

1. Implement the required interfaces in the modular framework
2. Register the implementation in the registry
3. Update the comparison scripts to include your new implementation

### Modifying Test Data
By default, the scripts will use `mock_data.csv` if available, or create a sample file if not. To use your own data:

1. Create a CSV file with your test data
2. Name it `mock_data.csv` or modify the scripts to use your file

## 🔬 Advanced Usage

### Running Multiple Tests
To run multiple tests and average the results:

```bash
for i in {1..5}; do ./compare.sh quick; done
```

### Comparing Different Data Sizes
To compare performance with different data sizes:

1. Create multiple data files with different sizes
2. Modify the scripts to use these files
3. Run the comparison for each file

### Profiling
For more detailed profiling:

```bash
go run comparison_demo.go v2 | tee results.txt
```

## 📚 Further Reading

For more information on the UltraFast indexing system and its modular architecture, see:
- `README_NEW.md` - Overview of the modular architecture
- `RESTRUCTURE_SUMMARY.md` - Technical details of the restructuring
- `FINAL_SUMMARY.md` - Summary of the modular system benefits
