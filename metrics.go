package main

import (
	"runtime"
	"sync"
	"time"
)

// SystemMetricsCollector implements MetricsCollector interface
type SystemMetricsCollector struct {
	startTime    time.Time
	startMemory  runtime.MemStats
	peakMemory   int64
	collecting   bool
	mutex        sync.RWMutex
	stopChan     chan bool
	metrics      map[string]interface{}
}

// NewSystemMetricsCollector creates a new metrics collector
func NewSystemMetricsCollector() *SystemMetricsCollector {
	return &SystemMetricsCollector{
		stopChan: make(chan bool, 1),
		metrics:  make(map[string]interface{}),
	}
}

// StartCollection begins metrics collection
func (smc *SystemMetricsCollector) StartCollection() error {
	smc.mutex.Lock()
	defer smc.mutex.Unlock()

	if smc.collecting {
		return nil // Already collecting
	}

	smc.startTime = time.Now()
	runtime.ReadMemStats(&smc.startMemory)
	smc.peakMemory = int64(smc.startMemory.Alloc)
	smc.collecting = true

	// Start background monitoring
	go smc.monitorMetrics()

	return nil
}

// StopCollection ends metrics collection and returns results
func (smc *SystemMetricsCollector) StopCollection() (map[string]interface{}, error) {
	smc.mutex.Lock()
	defer smc.mutex.Unlock()

	if !smc.collecting {
		return smc.metrics, nil
	}

	// Stop background monitoring
	smc.stopChan <- true
	smc.collecting = false

	// Collect final metrics
	var endMemory runtime.MemStats
	runtime.ReadMemStats(&endMemory)

	duration := time.Since(smc.startTime)

	smc.metrics["duration"] = duration
	smc.metrics["start_memory"] = int64(smc.startMemory.Alloc)
	smc.metrics["end_memory"] = int64(endMemory.Alloc)
	smc.metrics["peak_memory"] = smc.peakMemory
	smc.metrics["memory_delta"] = int64(endMemory.Alloc) - int64(smc.startMemory.Alloc)
	smc.metrics["gc_runs"] = endMemory.NumGC - smc.startMemory.NumGC
	smc.metrics["total_alloc"] = int64(endMemory.TotalAlloc - smc.startMemory.TotalAlloc)
	smc.metrics["mallocs"] = endMemory.Mallocs - smc.startMemory.Mallocs
	smc.metrics["frees"] = endMemory.Frees - smc.startMemory.Frees

	return smc.metrics, nil
}

// GetMemoryUsage returns current memory usage
func (smc *SystemMetricsCollector) GetMemoryUsage() int64 {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	return int64(m.Alloc)
}

// GetDiskIO returns disk I/O statistics (simplified implementation)
func (smc *SystemMetricsCollector) GetDiskIO() (reads int64, writes int64) {
	// This is a simplified implementation
	// In a real system, you would read from /proc/diskstats on Linux
	// or use platform-specific APIs
	return 0, 0
}

// Reset clears all collected metrics
func (smc *SystemMetricsCollector) Reset() {
	smc.mutex.Lock()
	defer smc.mutex.Unlock()

	smc.metrics = make(map[string]interface{})
	smc.peakMemory = 0
	smc.collecting = false
}

// monitorMetrics runs in background to track peak memory usage
func (smc *SystemMetricsCollector) monitorMetrics() {
	ticker := time.NewTicker(100 * time.Millisecond)
	defer ticker.Stop()

	for {
		select {
		case <-smc.stopChan:
			return
		case <-ticker.C:
			currentMemory := smc.GetMemoryUsage()
			smc.mutex.Lock()
			if currentMemory > smc.peakMemory {
				smc.peakMemory = currentMemory
			}
			smc.mutex.Unlock()
		}
	}
}

// BenchmarkTimer provides high-precision timing for benchmarks
type BenchmarkTimer struct {
	startTime time.Time
	endTime   time.Time
}

// NewBenchmarkTimer creates a new timer
func NewBenchmarkTimer() *BenchmarkTimer {
	return &BenchmarkTimer{}
}

// Start begins timing
func (bt *BenchmarkTimer) Start() {
	bt.startTime = time.Now()
}

// Stop ends timing
func (bt *BenchmarkTimer) Stop() time.Duration {
	bt.endTime = time.Now()
	return bt.endTime.Sub(bt.startTime)
}

// Elapsed returns the elapsed time
func (bt *BenchmarkTimer) Elapsed() time.Duration {
	if bt.endTime.IsZero() {
		return time.Since(bt.startTime)
	}
	return bt.endTime.Sub(bt.startTime)
}

// PerformanceProfiler provides detailed performance profiling
type PerformanceProfiler struct {
	timers   map[string]*BenchmarkTimer
	counters map[string]int64
	mutex    sync.RWMutex
}

// NewPerformanceProfiler creates a new profiler
func NewPerformanceProfiler() *PerformanceProfiler {
	return &PerformanceProfiler{
		timers:   make(map[string]*BenchmarkTimer),
		counters: make(map[string]int64),
	}
}

// StartTimer starts a named timer
func (pp *PerformanceProfiler) StartTimer(name string) {
	pp.mutex.Lock()
	defer pp.mutex.Unlock()

	timer := NewBenchmarkTimer()
	timer.Start()
	pp.timers[name] = timer
}

// StopTimer stops a named timer and returns the duration
func (pp *PerformanceProfiler) StopTimer(name string) time.Duration {
	pp.mutex.Lock()
	defer pp.mutex.Unlock()

	if timer, exists := pp.timers[name]; exists {
		return timer.Stop()
	}
	return 0
}

// IncrementCounter increments a named counter
func (pp *PerformanceProfiler) IncrementCounter(name string) {
	pp.mutex.Lock()
	defer pp.mutex.Unlock()

	pp.counters[name]++
}

// AddToCounter adds a value to a named counter
func (pp *PerformanceProfiler) AddToCounter(name string, value int64) {
	pp.mutex.Lock()
	defer pp.mutex.Unlock()

	pp.counters[name] += value
}

// GetResults returns all profiling results
func (pp *PerformanceProfiler) GetResults() map[string]interface{} {
	pp.mutex.RLock()
	defer pp.mutex.RUnlock()

	results := make(map[string]interface{})

	// Add timer results
	for name, timer := range pp.timers {
		results[name+"_duration"] = timer.Elapsed()
	}

	// Add counter results
	for name, count := range pp.counters {
		results[name+"_count"] = count
	}

	return results
}

// Reset clears all profiling data
func (pp *PerformanceProfiler) Reset() {
	pp.mutex.Lock()
	defer pp.mutex.Unlock()

	pp.timers = make(map[string]*BenchmarkTimer)
	pp.counters = make(map[string]int64)
}
