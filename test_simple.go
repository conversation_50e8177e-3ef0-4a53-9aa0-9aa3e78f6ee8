package main

import (
	"fmt"
	"os"
)

// Simple test to verify the modular system works
func testModularSystem() {
	fmt.Println("🧪 Testing Modular System")
	fmt.Println("========================")
	
	// Test 1: Initialize registry
	fmt.Println("\n1. Testing Registry...")
	InitializeRegistry()
	
	implementations := GetAllImplementations()
	if len(implementations) == 0 {
		fmt.Println("❌ No implementations found")
		return
	}
	
	fmt.Printf("✅ Found %d implementation(s)\n", len(implementations))
	for _, impl := range implementations {
		fmt.Printf("   - %s (%s)\n", impl.Name, impl.Generator.GetApproach())
	}
	
	// Test 2: Data generation
	fmt.Println("\n2. Testing Data Generation...")
	generator := NewTestDataGenerator()
	
	schema := map[string]string{
		"protocol": "string",
		"action":   "string",
	}
	
	dataset, err := generator.GenerateRealisticData(100, schema)
	if err != nil {
		fmt.Printf("❌ Data generation failed: %v\n", err)
		return
	}
	
	fmt.Printf("✅ Generated %d records with %d columns\n", dataset.Size, len(dataset.ColumnData))
	
	// Test 3: V2 Implementation
	fmt.Println("\n3. Testing V2 Implementation...")
	v2Impl, err := GetImplementation(ApproachV2)
	if err != nil {
		fmt.Printf("❌ Failed to get V2 implementation: %v\n", err)
		return
	}
	
	fmt.Printf("✅ V2 Implementation loaded: %s\n", v2Impl.Name)
	fmt.Printf("   Features: %d\n", len(v2Impl.Generator.GetFeatures()))
	fmt.Printf("   Operators: %v\n", v2Impl.QueryEngine.GetSupportedOperators())
	
	// Test 4: Index generation
	fmt.Println("\n4. Testing Index Generation...")
	outputDir := "./test_simple_temp"
	os.RemoveAll(outputDir)
	defer os.RemoveAll(outputDir)
	
	stats, err := v2Impl.Generator.GenerateMultiColumnIndex(dataset.ColumnData, outputDir, "test_table")
	if err != nil {
		fmt.Printf("❌ Index generation failed: %v\n", err)
		return
	}
	
	fmt.Printf("✅ Generated indexes for %d columns\n", len(stats))
	for columnName, stat := range stats {
		fmt.Printf("   %s: %d records, %s\n", columnName, stat.RecordCount, formatBytesSimple(stat.FileSize))
	}
	
	// Test 5: Query execution
	fmt.Println("\n5. Testing Query Execution...")
	if err := v2Impl.QueryEngine.Initialize(outputDir); err != nil {
		fmt.Printf("❌ Query engine initialization failed: %v\n", err)
		return
	}
	defer v2Impl.QueryEngine.Close()
	
	if len(dataset.QuerySet) > 0 {
		query := dataset.QuerySet[0]
		result, err := v2Impl.QueryEngine.ExecuteQuery(query.Column, query)
		if err != nil {
			fmt.Printf("❌ Query execution failed: %v\n", err)
			return
		}
		
		fmt.Printf("✅ Query executed successfully\n")
		fmt.Printf("   Query: %s = %v\n", query.Column, query.Value)
		fmt.Printf("   Results: %d\n", result.ResultCount)
		fmt.Printf("   Time: %v\n", result.ExecutionTime)
	}
	
	fmt.Println("\n🎉 All tests passed!")
}

// formatBytesSimple formats bytes for display
func formatBytesSimple(bytes int64) string {
	if bytes < 1024 {
		return fmt.Sprintf("%d B", bytes)
	} else if bytes < 1024*1024 {
		return fmt.Sprintf("%.1f KB", float64(bytes)/1024)
	} else {
		return fmt.Sprintf("%.1f MB", float64(bytes)/(1024*1024))
	}
}

// Entry point for simple test
func main() {
	if len(os.Args) > 1 && os.Args[1] == "test" {
		testModularSystem()
	} else {
		fmt.Println("🚀 UltraFast Modular System - Simple Test")
		fmt.Println("Usage: go run test_simple.go test")
		fmt.Println()
		fmt.Println("This will test the basic functionality of the modular system.")
	}
}
