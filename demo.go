package main

import (
	"fmt"
	"os"
	"strings"
	"time"
)

// Demo demonstrates the new modular indexing system
func runDemo() {
	fmt.Println("🚀 UltraFast Modular Indexing System Demo")
	fmt.Println(strings.Repeat("=", 60))
	fmt.Println()

	// Initialize registry
	InitializeRegistry()

	// Show available implementations
	fmt.Println("📦 Available Implementations:")
	implementations := GetAllImplementations()
	for i, impl := range implementations {
		fmt.Printf("  %d. %s - %s\n", i+1, impl.Name, impl.Description)
	}
	fmt.Println()

	// Generate test data
	fmt.Println("📊 Generating test data...")
	generator := NewTestDataGenerator()
	
	schema := map[string]string{
		"protocol":        "string",
		"rule_name":       "string", 
		"source_ip":       "ip",
		"dest_ip":         "ip",
		"action":          "string",
		"source_username": "string",
	}
	
	dataset, err := generator.GenerateRealisticData(5000, schema)
	if err != nil {
		fmt.Printf("Error generating data: %v\n", err)
		return
	}
	
	fmt.Printf("✅ Generated %d records with %d columns\n", dataset.Size, len(dataset.ColumnData))
	fmt.Println()

	// Test each implementation
	for _, impl := range implementations {
		fmt.Printf("🔧 Testing %s...\n", impl.Name)
		fmt.Println(strings.Repeat("-", 40))
		
		if err := testImplementation(impl, dataset); err != nil {
			fmt.Printf("❌ Error: %v\n", err)
		} else {
			fmt.Printf("✅ Success!\n")
		}
		fmt.Println()
	}

	// Run performance comparison
	fmt.Println("⚡ Running Performance Comparison...")
	fmt.Println(strings.Repeat("=", 60))
	
	benchmark := NewComprehensiveBenchmark()
	results, err := benchmark.RunComparison(implementations, dataset)
	if err != nil {
		fmt.Printf("Error running comparison: %v\n", err)
		return
	}

	// Generate report
	report, err := benchmark.GenerateReport(results)
	if err != nil {
		fmt.Printf("Error generating report: %v\n", err)
		return
	}

	fmt.Println(report)
}

// testImplementation tests a single implementation
func testImplementation(impl *IndexingImplementation, dataset TestDataset) error {
	outputDir := fmt.Sprintf("./demo_temp/%s", impl.Generator.GetApproach())
	
	// Clean up any existing files
	os.RemoveAll(outputDir)
	defer os.RemoveAll(outputDir)
	
	// Test index generation
	fmt.Printf("  📝 Generating indexes...")
	start := time.Now()
	stats, err := impl.Generator.GenerateMultiColumnIndex(dataset.ColumnData, outputDir, "demo_table")
	if err != nil {
		return fmt.Errorf("index generation failed: %v", err)
	}
	genTime := time.Since(start)
	fmt.Printf(" %v\n", genTime)
	
	// Show generation stats
	totalSize := int64(0)
	for _, stat := range stats {
		totalSize += stat.FileSize
	}
	fmt.Printf("  📁 Total index size: %s\n", formatBytes(totalSize))
	
	// Test query engine
	fmt.Printf("  🔍 Testing queries...")
	if err := impl.QueryEngine.Initialize(outputDir); err != nil {
		return fmt.Errorf("query engine initialization failed: %v", err)
	}
	defer impl.QueryEngine.Close()
	
	// Run test queries
	queryCount := 0
	totalQueryTime := time.Duration(0)
	
	for _, query := range dataset.QuerySet {
		if queryCount >= 5 { // Limit to 5 queries for demo
			break
		}
		
		start := time.Now()
		result, err := impl.QueryEngine.ExecuteQuery(query.Column, query)
		queryTime := time.Since(start)
		
		if err != nil {
			fmt.Printf("\n    ❌ Query failed: %v", err)
			continue
		}
		
		totalQueryTime += queryTime
		queryCount++
		
		if queryCount == 1 {
			fmt.Printf(" %v", queryTime)
		}
	}
	
	if queryCount > 1 {
		avgTime := totalQueryTime / time.Duration(queryCount)
		fmt.Printf(" (avg: %v)", avgTime)
	}
	fmt.Println()
	
	// Test validation
	fmt.Printf("  ✅ Validating indexes...")
	validationCount := 0
	for columnName := range dataset.ColumnData {
		var indexFile string
		switch impl.Generator.GetApproach() {
		case ApproachV2:
			indexFile = fmt.Sprintf("%s/%s_ultrafast_v2.ufidx", outputDir, columnName)
		case ApproachV3:
			indexFile = fmt.Sprintf("%s/%s_ultrafast_v3.uf3", outputDir, columnName)
		default:
			continue
		}
		
		if err := impl.Generator.ValidateIndex(indexFile); err != nil {
			return fmt.Errorf("validation failed for %s: %v", columnName, err)
		}
		validationCount++
		
		if validationCount >= 3 { // Limit validation tests
			break
		}
	}
	fmt.Printf(" %d files validated\n", validationCount)
	
	return nil
}

// demoMain is the entry point for the demo
func demoMain() {
	if len(os.Args) > 1 && os.Args[1] == "demo" {
		runDemo()
	} else {
		// Run the regular main function
		main()
	}
}

// Quick demo function for testing specific features
func quickDemo() {
	fmt.Println("🔧 Quick Demo - Testing Core Functionality")
	fmt.Println()
	
	// Initialize registry
	InitializeRegistry()
	
	// Test V2 implementation
	fmt.Println("Testing V2 Implementation:")
	v2Impl, err := GetImplementation(ApproachV2)
	if err != nil {
		fmt.Printf("Error getting V2 implementation: %v\n", err)
		return
	}
	
	fmt.Printf("  Name: %s\n", v2Impl.Name)
	fmt.Printf("  Features: %s\n", strings.Join(v2Impl.Generator.GetFeatures(), ", "))
	fmt.Printf("  Operators: %s\n", strings.Join(v2Impl.QueryEngine.GetSupportedOperators(), ", "))
	fmt.Println()
	
	// Test V3 implementation
	fmt.Println("Testing V3 Implementation:")
	v3Impl, err := GetImplementation(ApproachV3)
	if err != nil {
		fmt.Printf("Error getting V3 implementation: %v\n", err)
		return
	}
	
	fmt.Printf("  Name: %s\n", v3Impl.Name)
	fmt.Printf("  Features: %s\n", strings.Join(v3Impl.Generator.GetFeatures(), ", "))
	fmt.Printf("  Operators: %s\n", strings.Join(v3Impl.QueryEngine.GetSupportedOperators(), ", "))
	fmt.Println()
	
	// Test data generation
	fmt.Println("Testing Data Generation:")
	generator := NewTestDataGenerator()
	
	schema := map[string]string{
		"protocol": "string",
		"action":   "string",
	}
	
	dataset, err := generator.GenerateRealisticData(100, schema)
	if err != nil {
		fmt.Printf("Error generating data: %v\n", err)
		return
	}
	
	fmt.Printf("  Generated %d records\n", dataset.Size)
	fmt.Printf("  Columns: %d\n", len(dataset.ColumnData))
	fmt.Printf("  Queries: %d\n", len(dataset.QuerySet))
	
	for columnName, records := range dataset.ColumnData {
		fmt.Printf("    %s: %d records\n", columnName, len(records))
	}
	
	fmt.Println("\n✅ Quick demo completed successfully!")
}
