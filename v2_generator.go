package main

import (
	"bytes"
	"encoding/binary"
	"fmt"
	"hash/crc32"
	"math"
	"os"
	"sort"
	"time"
	"unsafe"

	"github.com/RoaringBitmap/roaring"
)

// V2Generator implements IndexGenerator for UltraFast V2 format
type V2Generator struct {
	outputDir string
}

// NewV2Generator creates a new V2 format generator
func NewV2Generator(outputDir string) *V2Generator {
	return &V2Generator{outputDir: outputDir}
}

// GetApproach returns the indexing approach identifier
func (g *V2Generator) GetApproach() IndexingApproach {
	return ApproachV2
}

// GetFeatures returns a list of features supported by this approach
func (g *V2Generator) GetFeatures() []string {
	return []string{
		"Hash Table Indexing",
		"CRC32C Hashing",
		"Roaring Bitmap Compression",
		"Bloom Filters",
		"Memory Mapping",
		"Key Prefixes",
		"Quadratic Probing",
		"SIMD Optimizations",
	}
}

// GenerateIndex creates an index for the given column data
func (g *V2Generator) GenerateIndex(columnName string, records []Record, outputDir string) (*IndexStats, error) {
	if outputDir != "" {
		g.outputDir = outputDir
	}

	start := time.Now()

	// Group records by value
	valueMap := make(map[string][]uint32)
	for _, record := range records {
		valueMap[record.Value] = append(valueMap[record.Value], record.LineNumber)
	}

	// Sort line numbers for each value
	for value := range valueMap {
		sort.Slice(valueMap[value], func(i, j int) bool {
			return valueMap[value][i] < valueMap[value][j]
		})
	}

	// Sort keys for consistent ordering
	keys := make([]string, 0, len(valueMap))
	for key := range valueMap {
		keys = append(keys, key)
	}
	sort.Strings(keys)

	// Create output directory
	if err := os.MkdirAll(g.outputDir, 0755); err != nil {
		return nil, err
	}

	filename := fmt.Sprintf("%s/%s_ultrafast_v2.ufidx", g.outputDir, columnName)
	file, err := os.Create(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	// Create bloom filter
	bloomFilter := NewBloomFilter(uint32(len(keys)), 0.01)
	for _, key := range keys {
		bloomFilter.Add(key)
	}

	// Build hash table with load factor 0.7
	hashTableSize := nextPowerOf2(uint32(float64(len(keys)) / 0.7))
	hashTable := make([]uint32, hashTableSize)

	// Use quadratic probing for collision resolution
	for i, key := range keys {
		hash := crc32cHash(key)
		initialHash := hash % hashTableSize
		var probe uint32 = 0
		currentPos := initialHash

		for hashTable[currentPos] != 0 {
			probe++
			currentPos = (initialHash + probe*probe) % hashTableSize
		}
		hashTable[currentPos] = uint32(i + 1) // 1-based indexing
	}

	// Write file header
	keyEntrySize := uint32(unsafe.Sizeof(CompressedKeyEntry{}))
	header := V2Header{
		Version:         2,
		NumKeys:         uint32(len(keys)),
		HashTableSize:   hashTableSize,
		KeyEntrySize:    keyEntrySize,
		BloomFilterSize: uint32(len(bloomFilter.bits) * 8),
	}
	copy(header.Magic[:], V2_MAGIC_NUMBER)

	if err := binary.Write(file, binary.LittleEndian, header); err != nil {
		return nil, err
	}

	// Write bloom filter
	bloomBytes := make([]byte, len(bloomFilter.bits)*8)
	for i, word := range bloomFilter.bits {
		binary.LittleEndian.PutUint64(bloomBytes[i*8:(i+1)*8], word)
	}
	if _, err := file.Write(bloomBytes); err != nil {
		return nil, err
	}

	// Write hash table
	hashTableBytes := make([]byte, hashTableSize*4)
	for i, val := range hashTable {
		binary.LittleEndian.PutUint32(hashTableBytes[i*4:(i+1)*4], val)
	}
	if _, err := file.Write(hashTableBytes); err != nil {
		return nil, err
	}

	// Write key directory and data section
	keyDirectory := make([]CompressedKeyEntry, len(keys))
	var dataSection bytes.Buffer

	for i, key := range keys {
		lineNumbers := valueMap[key]
		compressedData, err := compressLineNumbersRoaring(lineNumbers)
		if err != nil {
			return nil, fmt.Errorf("failed to compress roaring bitmap for key %s: %v", key, err)
		}

		keyDirectory[i] = CompressedKeyEntry{
			KeyHash:    crc32cHash(key),
			DataOffset: uint32(dataSection.Len()),
			Count:      uint32(len(lineNumbers)),
		}
		copy(keyDirectory[i].KeyPrefix[:], key) // Copy prefix

		// Store key length + key + compressed data
		binary.Write(&dataSection, binary.LittleEndian, uint16(len(key)))
		dataSection.WriteString(key)
		dataSection.Write(compressedData)
	}

	// Write key directory
	for _, entry := range keyDirectory {
		if err := binary.Write(file, binary.LittleEndian, entry); err != nil {
			return nil, err
		}
	}

	// Write data section
	if _, err := file.Write(dataSection.Bytes()); err != nil {
		return nil, err
	}

	buildTime := time.Since(start)

	// Get file stats
	fileInfo, err := file.Stat()
	if err != nil {
		return nil, err
	}

	return &IndexStats{
		Approach:         ApproachV2,
		ColumnName:       columnName,
		RecordCount:      uint32(len(records)),
		UniqueValues:     uint32(len(keys)),
		FileSize:         fileInfo.Size(),
		CompressionRatio: g.calculateCompressionRatio(records, fileInfo.Size()),
		BuildTime:        buildTime,
		Features:         g.GetFeatures(),
	}, nil
}

// GenerateMultiColumnIndex creates indexes for multiple columns
func (g *V2Generator) GenerateMultiColumnIndex(columnData map[string][]Record, outputDir string, tableName string) (map[string]*IndexStats, error) {
	if outputDir != "" {
		g.outputDir = outputDir
	}

	stats := make(map[string]*IndexStats)

	for columnName, records := range columnData {
		indexStats, err := g.GenerateIndex(columnName, records, outputDir)
		if err != nil {
			return nil, fmt.Errorf("failed to generate index for column %s: %v", columnName, err)
		}
		stats[columnName] = indexStats
	}

	return stats, nil
}

// ValidateIndex validates the integrity of an index file
func (g *V2Generator) ValidateIndex(indexPath string) error {
	file, err := os.Open(indexPath)
	if err != nil {
		return err
	}
	defer file.Close()

	// Read and validate header
	var header V2Header
	if err := binary.Read(file, binary.LittleEndian, &header); err != nil {
		return err
	}

	// Check magic number
	if string(header.Magic[:]) != V2_MAGIC_NUMBER {
		return fmt.Errorf("invalid magic number: expected %s, got %s", V2_MAGIC_NUMBER, string(header.Magic[:]))
	}

	// Validate version
	if header.Version != 2 {
		return fmt.Errorf("unsupported version: %d", header.Version)
	}

	return nil
}

// GetIndexStats returns statistics about an existing index
func (g *V2Generator) GetIndexStats(indexPath string) (*IndexStats, error) {
	file, err := os.Open(indexPath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	// Read header
	var header V2Header
	if err := binary.Read(file, binary.LittleEndian, &header); err != nil {
		return nil, err
	}

	// Get file size
	fileInfo, err := file.Stat()
	if err != nil {
		return nil, err
	}

	return &IndexStats{
		Approach:     ApproachV2,
		RecordCount:  header.NumKeys, // This is actually unique values, not total records
		UniqueValues: header.NumKeys,
		FileSize:     fileInfo.Size(),
		Features:     g.GetFeatures(),
	}, nil
}

// calculateCompressionRatio estimates compression ratio
func (g *V2Generator) calculateCompressionRatio(records []Record, indexSize int64) float64 {
	// Estimate raw data size
	estimatedRawSize := int64(0)
	for _, record := range records {
		estimatedRawSize += int64(len(record.Value)) + 8 // value + line number
	}

	if estimatedRawSize > 0 {
		return 1.0 - float64(indexSize)/float64(estimatedRawSize)
	}

	return 0.0
}
