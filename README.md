# UltraFast V2 Index

A high-performance indexing system for CSV data with **microsecond-level query performance**.

## 🚀 Features

- **⚡ Ultra-fast queries**: Sub-millisecond performance (400-900µs)
- **🗜️ Advanced compression**: Roaring bitmap compression for optimal storage
- **🔍 Bloom filters**: Fast negative lookups to avoid disk I/O
- **🏗️ Hash table indexing**: CRC32C-based O(1) average lookup time
- **💾 Memory-mapped files**: Efficient memory usage with mmap
- **🎯 SIMD optimizations**: Vectorized operations for maximum performance

## 📊 Performance

- **Query time**: 400-900 microseconds
- **Index generation**: 1-2 seconds for 33K records
- **Storage efficiency**: Compressed binary format
- **Throughput**: 1000+ queries per second

## 🛠️ Quick Start

### 1. Generate V2 Indexes

```bash
go run main.go ultrafast_v2.go generate mock_data.csv ./indexes demo_table
```

### 2. Query Data

```bash
# Simple equality query
go run main.go ultrafast_v2.go query ./indexes demo_table "protocol=TCP" protocol

# Query with different columns
go run main.go ultrafast_v2.go query ./indexes demo_table "source_username=john_doe"
go run main.go ultrafast_v2.go query ./indexes demo_table "action=ALLOW"
```

### 3. Validate Indexes

```bash
go run main.go ultrafast_v2.go validate ./indexes/protocol_ultrafast_v2.ufidx
```

### 4. Show Statistics

```bash
go run main.go ultrafast_v2.go stats ./indexes protocol
```

## 📁 File Structure

```
ultrafast_standalone/
├── main.go              # Main CLI interface
├── ultrafast_v2.go      # V2 indexing and query engine
├── mock_data.csv        # Sample dataset (33K records)
├── demo_results/        # Generated indexes
│   └── ultrafast_v2/    # V2 format indexes
└── README.md           # This file
```

## 🔧 Commands

| Command | Description | Example |
|---------|-------------|---------|
| `generate` | Create V2 indexes for all columns | `go run main.go ultrafast_v2.go generate data.csv ./indexes table_name` |
| `query` | Execute equality queries | `go run main.go ultrafast_v2.go query ./indexes table "column=value"` |
| `validate` | Validate index file integrity | `go run main.go ultrafast_v2.go validate ./indexes/file.ufidx` |
| `stats` | Show index statistics | `go run main.go ultrafast_v2.go stats ./indexes column_name` |
| `benchmark` | Run performance benchmarks | `go run main.go ultrafast_v2.go benchmark ./indexes queries.txt` |

## 🏗️ V2 File Format

UltraFast V2 uses an optimized binary format (`.ufidx`) with:

- **Magic Number**: `UFASTV2\x00` (8 bytes)
- **Header**: Metadata with version, counts, sizes (128 bytes)
- **Bloom Filter**: Fast negative lookup filter
- **Hash Table**: CRC32C-based key distribution
- **Key Directory**: Compressed key entries with prefixes
- **Data Section**: Roaring bitmap compressed line numbers

## 🎯 Use Cases

- **Log analysis**: Fast filtering of network/security logs
- **Data analytics**: Quick aggregations on large datasets  
- **Real-time queries**: Sub-millisecond response requirements
- **ETL pipelines**: High-performance data processing

## 🔬 Technical Details

- **Hash Function**: CRC32C (Castagnoli) for optimal distribution
- **Compression**: Roaring bitmaps for line number storage
- **Memory Mapping**: Zero-copy file access with mmap
- **Bloom Filter**: Configurable false positive rate
- **Key Prefixes**: 8-byte prefixes for collision avoidance

## 📈 Benchmarks

Based on 33K record dataset:

```
Implementation | Query Time | Storage | Throughput
---------------|------------|---------|------------
UltraFast V2   | ~500µs     | ~2MB    | 2000+ QPS
```

## 🚀 Getting Started

1. **Clone and build**:
   ```bash
   git clone <repository>
   cd ultrafast_standalone
   ```

2. **Generate indexes**:
   ```bash
   go run main.go ultrafast_v2.go generate mock_data.csv ./demo_results/ultrafast_v2 demo_table
   ```

3. **Run queries**:
   ```bash
   go run main.go ultrafast_v2.go query ./demo_results/ultrafast_v2 demo_table "protocol=TCP" protocol
   ```

## 📝 License

This project is part of the UltraFast indexing research and development.
