package main

import (
	"fmt"
	"os"
	"strconv"
	"strings"
)

func main() {
	// Initialize the registry with all available implementations
	InitializeRegistry()

	if len(os.Args) < 2 {
		printUsage()
		return
	}

	command := os.Args[1]

	switch command {
	case "generate":
		handleGenerate()
	case "query":
		handleQuery()
	case "benchmark":
		handleBenchmark()
	case "compare":
		handleCompare()
	case "validate":
		handleValidate()
	case "stats":
		handleStats()
	case "list":
		handleList()
	default:
		fmt.Printf("Unknown command: %s\n", command)
		printUsage()
	}
}

func printUsage() {
	fmt.Println("🚀 UltraFast Multi-Implementation Indexing System")
	fmt.Println()
	fmt.Println("Usage:")
	fmt.Println("  ultrafast generate <approach> <csv_file> <output_dir> <table_name>")
	fmt.Println("  ultrafast query <approach> <index_dir> <table_name> <filter_expression>")
	fmt.Println("  ultrafast benchmark <approach> <index_dir> <queries_file>")
	fmt.Println("  ultrafast compare <csv_file> <output_dir> <table_name> [test_size]")
	fmt.Println("  ultrafast validate <approach> <index_file>")
	fmt.Println("  ultrafast stats <approach> <index_dir> <column_name>")
	fmt.Println("  ultrafast list")
	fmt.Println()
	fmt.Println("Available approaches:")
	for _, approach := range GetAvailableApproaches() {
		impl, _ := GetImplementation(approach)
		fmt.Printf("  %-15s - %s\n", approach, impl.Description)
	}
	fmt.Println()
	fmt.Println("Examples:")
	fmt.Println("  ultrafast generate ultrafast_v2 mock_data.csv ./indexes demo_table")
	fmt.Println("  ultrafast query ultrafast_v2 ./indexes demo_table \"protocol=TCP\"")
	fmt.Println("  ultrafast compare mock_data.csv ./comparison demo_table 10000")
	fmt.Println("  ultrafast list")
}

func handleGenerate() {
	if len(os.Args) < 6 {
		fmt.Println("Usage: ultrafast generate <approach> <csv_file> <output_dir> <table_name>")
		return
	}

	approach := IndexingApproach(os.Args[2])
	csvFile := os.Args[3]
	outputDir := os.Args[4]
	tableName := os.Args[5]

	impl, err := GetImplementation(approach)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}

	fmt.Printf("Generating %s indexes for table '%s'...\n", impl.Name, tableName)

	// Load data from CSV
	generator := NewTestDataGenerator()
	dataset, err := generator.LoadFromCSV(csvFile)
	if err != nil {
		fmt.Printf("Error loading CSV: %v\n", err)
		return
	}

	// Generate indexes
	stats, err := impl.Generator.GenerateMultiColumnIndex(dataset.ColumnData, outputDir, tableName)
	if err != nil {
		fmt.Printf("Error generating indexes: %v\n", err)
		return
	}

	// Display results
	fmt.Printf("✅ Indexes generated successfully!\n")
	fmt.Printf("📊 Statistics:\n")
	for columnName, stat := range stats {
		fmt.Printf("  %s: %d records, %d unique values, %s, %.1f%% compression\n",
			columnName, stat.RecordCount, stat.UniqueValues,
			formatBytes(stat.FileSize), stat.CompressionRatio*100)
	}
}

func handleQuery() {
	if len(os.Args) < 6 {
		fmt.Println("Usage: ultrafast query <approach> <index_dir> <table_name> <filter_expression>")
		fmt.Println("Examples:")
		fmt.Println("  ultrafast query ultrafast_v2 ./indexes demo_table \"protocol=TCP\"")
		fmt.Println("  ultrafast query ultrafast_v3 ./indexes demo_table \"source_username=john_doe\"")
		return
	}

	approach := IndexingApproach(os.Args[2])
	indexDir := os.Args[3]
	tableName := os.Args[4]
	filterExpr := os.Args[5]

	impl, err := GetImplementation(approach)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}

	fmt.Printf("Querying with %s: %s\n", impl.Name, filterExpr)

	// Parse filter expression (simplified)
	parts := strings.Split(filterExpr, "=")
	if len(parts) != 2 {
		fmt.Printf("Error: Only simple equality queries supported (column=value)\n")
		return
	}

	filter := QueryFilter{
		Column:   strings.TrimSpace(parts[0]),
		Operator: "=",
		Value:    strings.TrimSpace(parts[1]),
	}

	// Initialize query engine
	if err := impl.QueryEngine.Initialize(indexDir); err != nil {
		fmt.Printf("Error initializing query engine: %v\n", err)
		return
	}
	defer impl.QueryEngine.Close()

	// Execute query
	result, err := impl.QueryEngine.ExecuteQuery(filter.Column, filter)
	if err != nil {
		fmt.Printf("Error executing query: %v\n", err)
		return
	}

	// Display results
	fmt.Printf("✅ Query executed in %v\n", result.ExecutionTime)
	fmt.Printf("📊 Found %d results\n", result.ResultCount)
	if result.ResultCount > 0 && result.ResultCount <= 10 {
		fmt.Printf("Line numbers: %v\n", result.LineNumbers)
	} else if result.ResultCount > 10 {
		fmt.Printf("Line numbers: %v... (%d more)\n", result.LineNumbers[:10], result.ResultCount-10)
	}
}

func handleBenchmark() {
	if len(os.Args) < 5 {
		fmt.Println("Usage: ultrafast benchmark <approach> <index_dir> <queries_file>")
		return
	}

	approach := IndexingApproach(os.Args[2])
	indexDir := os.Args[3]
	queriesFile := os.Args[4]

	impl, err := GetImplementation(approach)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}

	fmt.Printf("Running benchmark with %s\n", impl.Name)
	fmt.Printf("Index directory: %s\n", indexDir)
	fmt.Printf("Queries file: %s\n", queriesFile)

	// TODO: Implement benchmark logic
	fmt.Println("Benchmark functionality coming soon...")
}

func handleCompare() {
	if len(os.Args) < 5 {
		fmt.Println("Usage: ultrafast compare <csv_file> <output_dir> <table_name> [test_size]")
		return
	}

	csvFile := os.Args[2]
	outputDir := os.Args[3]
	tableName := os.Args[4]

	testSize := 10000 // Default test size
	if len(os.Args) > 5 {
		if size, err := strconv.Atoi(os.Args[5]); err == nil {
			testSize = size
		}
	}

	fmt.Printf("🚀 Running comprehensive comparison\n")
	fmt.Printf("📁 Data source: %s\n", csvFile)
	fmt.Printf("📊 Test size: %d records\n", testSize)
	fmt.Printf("📂 Output directory: %s\n", outputDir)
	fmt.Println(strings.Repeat("=", 60))

	// Load test data
	generator := NewTestDataGenerator()
	dataset, err := generator.LoadFromCSV(csvFile)
	if err != nil {
		fmt.Printf("Error loading CSV: %v\n", err)
		return
	}

	// Limit dataset size if requested
	if testSize < dataset.Size {
		for columnName, records := range dataset.ColumnData {
			if len(records) > testSize {
				dataset.ColumnData[columnName] = records[:testSize]
			}
		}
		dataset.Size = testSize
	}

	// Get all implementations
	implementations := GetAllImplementations()
	
	// Run comprehensive benchmark
	benchmark := NewComprehensiveBenchmark()
	results, err := benchmark.RunComparison(implementations, dataset)
	if err != nil {
		fmt.Printf("Error running comparison: %v\n", err)
		return
	}

	// Generate and display report
	report, err := benchmark.GenerateReport(results)
	if err != nil {
		fmt.Printf("Error generating report: %v\n", err)
		return
	}

	fmt.Println(report)
}

func handleValidate() {
	if len(os.Args) < 4 {
		fmt.Println("Usage: ultrafast validate <approach> <index_file>")
		return
	}

	approach := IndexingApproach(os.Args[2])
	indexFile := os.Args[3]

	impl, err := GetImplementation(approach)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}

	fmt.Printf("Validating %s index: %s\n", impl.Name, indexFile)

	if err := impl.Generator.ValidateIndex(indexFile); err != nil {
		fmt.Printf("❌ Validation failed: %v\n", err)
		return
	}

	fmt.Println("✅ Index file is valid")
}

func handleStats() {
	if len(os.Args) < 5 {
		fmt.Println("Usage: ultrafast stats <approach> <index_dir> <column_name>")
		return
	}

	approach := IndexingApproach(os.Args[2])
	indexDir := os.Args[3]
	columnName := os.Args[4]

	impl, err := GetImplementation(approach)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}

	fmt.Printf("Showing %s stats for column '%s'\n", impl.Name, columnName)

	// Construct index file path based on approach
	var indexFile string
	switch approach {
	case ApproachV2:
		indexFile = fmt.Sprintf("%s/%s_ultrafast_v2.ufidx", indexDir, columnName)
	case ApproachV3:
		indexFile = fmt.Sprintf("%s/%s_ultrafast_v3.uf3", indexDir, columnName)
	default:
		fmt.Printf("Unknown approach: %s\n", approach)
		return
	}

	stats, err := impl.Generator.GetIndexStats(indexFile)
	if err != nil {
		fmt.Printf("Error getting stats: %v\n", err)
		return
	}

	fmt.Printf("📊 Index Statistics:\n")
	fmt.Printf("  Approach: %s\n", stats.Approach)
	fmt.Printf("  Records: %d\n", stats.RecordCount)
	fmt.Printf("  Unique Values: %d\n", stats.UniqueValues)
	fmt.Printf("  File Size: %s\n", formatBytes(stats.FileSize))
	fmt.Printf("  Compression: %.1f%%\n", stats.CompressionRatio*100)
	fmt.Printf("  Features: %s\n", strings.Join(stats.Features, ", "))
}

func handleList() {
	fmt.Println("🔧 Available Indexing Implementations:")
	fmt.Println(strings.Repeat("=", 50))

	implementations := GetAllImplementations()
	for _, impl := range implementations {
		fmt.Printf("\n📦 %s (v%s)\n", impl.Name, impl.Version)
		fmt.Printf("   Approach: %s\n", impl.Generator.GetApproach())
		fmt.Printf("   Description: %s\n", impl.Description)
		fmt.Printf("   Features: %s\n", strings.Join(impl.Generator.GetFeatures(), ", "))
		fmt.Printf("   Operators: %s\n", strings.Join(impl.QueryEngine.GetSupportedOperators(), ", "))
	}
}

// Helper function to format bytes
func formatBytes(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}
