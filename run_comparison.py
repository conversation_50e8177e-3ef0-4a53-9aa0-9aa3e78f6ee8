#!/usr/bin/env python3

"""
UltraFast Performance Comparison Script
Usage: python3 run_comparison.py [demo|real|v2|quick]
"""

import os
import sys
import subprocess
import time
import shutil
from pathlib import Path

# Colors for output
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    PURPLE = '\033[0;35m'
    CYAN = '\033[0;36m'
    NC = '\033[0m'  # No Color

def print_header(text):
    print(f"{Colors.BLUE}🚀 {text}{Colors.NC}")
    print(f"{Colors.BLUE}{'=' * len(text)}{Colors.NC}")

def print_success(text):
    print(f"{Colors.GREEN}✅ {text}{Colors.NC}")

def print_error(text):
    print(f"{Colors.RED}❌ {text}{Colors.NC}")

def print_info(text):
    print(f"{Colors.CYAN}ℹ️  {text}{Colors.NC}")

def print_warning(text):
    print(f"{Colors.YELLOW}⚠️  {text}{Colors.NC}")

def check_file(filename):
    """Check if file exists"""
    if not Path(filename).exists():
        print_error(f"File {filename} not found")
        return False
    return True

def create_sample_data():
    """Create sample mock_data.csv if it doesn't exist"""
    print_info("Creating sample mock_data.csv...")
    
    sample_data = """protocol,rule_name,source_ip,dest_ip,action,source_username
TCP,ALLOW_WEB,************,********,ALLOW,john_doe
UDP,ALLOW_DNS,************,*******,ALLOW,alice
TCP,ALLOW_SSH,************,********,ALLOW,bob
HTTP,ALLOW_WEB,************,********,ALLOW,charlie
TCP,BLOCK_MALWARE,************,suspicious.com,BLOCK,system
UDP,ALLOW_DNS,************,*******,ALLOW,dave
HTTPS,ALLOW_WEB,************,********,ALLOW,eve
TCP,ALLOW_SSH,************,********,ALLOW,admin
TCP,ALLOW_WEB,************,********,ALLOW,guest
UDP,BLOCK_P2P,************,peer.network,BLOCK,user1"""
    
    with open("mock_data.csv", "w") as f:
        f.write(sample_data)
    
    print_success("Sample data created")

def run_demo():
    """Run simulated performance comparison demo"""
    print_header("UltraFast Performance Comparison Demo")
    print()
    
    print_info("Running simulated performance comparison...")
    try:
        subprocess.run(["go", "run", "comparison_demo.go", "demo"], check=True)
    except subprocess.CalledProcessError as e:
        print_error(f"Demo failed: {e}")
    except FileNotFoundError:
        print_error("Go not found. Please install Go and ensure it's in your PATH")

def run_real():
    """Run real performance comparison"""
    print_header("Real Performance Comparison")
    print()
    
    # Check prerequisites
    if not check_file("mock_data.csv"):
        print_warning("mock_data.csv not found. Creating sample data...")
        create_sample_data()
    
    if not check_file("main.go"):
        print_error("main.go not found")
        return
    
    print_info("Running real performance comparison...")
    try:
        subprocess.run(["go", "run", "comparison_demo.go", "real"], check=True)
    except subprocess.CalledProcessError as e:
        print_error(f"Real comparison failed: {e}")

def test_v2():
    """Test V2 implementation only"""
    print_header("Testing UltraFast V2 Implementation")
    print()
    
    if not check_file("ultrafast_v2.go"):
        print_error("ultrafast_v2.go not found")
        return
    
    if not check_file("mock_data.csv"):
        print_warning("mock_data.csv not found. Creating sample data...")
        create_sample_data()
    
    print_info("Testing V2 implementation...")
    try:
        subprocess.run(["go", "run", "comparison_demo.go", "v2"], check=True)
    except subprocess.CalledProcessError as e:
        print_error(f"V2 test failed: {e}")

def quick_benchmark():
    """Run quick benchmark with V2"""
    print_header("Quick Performance Benchmark")
    print()
    
    if not check_file("mock_data.csv"):
        create_sample_data()
    
    print_info("Running quick V2 benchmark...")
    
    # Clean up previous test
    if Path("quick_test").exists():
        shutil.rmtree("quick_test")
    
    try:
        # Test V2 generation
        print("📝 Index generation: ", end="", flush=True)
        start_time = time.time()
        
        result = subprocess.run([
            "go", "run", "main.go", "ultrafast_v2.go", 
            "generate", "mock_data.csv", "./quick_test", "demo_table"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            build_time = (time.time() - start_time) * 1000
            print(f"{build_time:.0f}ms")
            
            # Test V2 query
            print("🔍 Query execution: ", end="", flush=True)
            start_time = time.time()
            
            query_result = subprocess.run([
                "go", "run", "main.go", "ultrafast_v2.go",
                "query", "./quick_test", "demo_table", "protocol=TCP"
            ], capture_output=True, text=True)
            
            if query_result.returncode == 0:
                query_time = (time.time() - start_time) * 1000
                print(f"{query_time:.0f}ms")
                
                # Get index size
                try:
                    size_result = subprocess.run(["du", "-sh", "./quick_test"], 
                                               capture_output=True, text=True)
                    if size_result.returncode == 0:
                        index_size = size_result.stdout.split()[0]
                        print(f"📁 Index size: {index_size}")
                except:
                    print("📁 Index size: Unable to determine")
                
                # Parse results
                if "Found" in query_result.stdout:
                    for line in query_result.stdout.split('\n'):
                        if "Found" in line:
                            print(f"📊 {line.strip()}")
                            break
                
                print_success("Quick benchmark completed")
            else:
                print_error("Query failed")
        else:
            print_error("Index generation failed")
            
    except Exception as e:
        print_error(f"Benchmark failed: {e}")
    finally:
        # Clean up
        if Path("quick_test").exists():
            shutil.rmtree("quick_test")

def show_usage():
    """Show usage information"""
    print(f"{Colors.PURPLE}🚀 UltraFast Performance Comparison Script{Colors.NC}")
    print()
    print("Usage: python3 run_comparison.py [command]")
    print()
    print("Commands:")
    print("  demo     - Run simulated performance comparison demo")
    print("  real     - Run real performance comparison with existing implementations")
    print("  v2       - Test UltraFast V2 implementation only")
    print("  quick    - Run quick benchmark with V2")
    print("  help     - Show this help message")
    print()
    print("Examples:")
    print("  python3 run_comparison.py demo          # Run simulated demo")
    print("  python3 run_comparison.py real          # Test real implementations")
    print("  python3 run_comparison.py quick         # Quick V2 benchmark")
    print()
    print("Prerequisites:")
    print("  - Go installed and available in PATH")
    print("  - mock_data.csv (will be created if missing)")
    print("  - main.go, ultrafast_v2.go (for real tests)")

def main():
    """Main function"""
    if len(sys.argv) < 2:
        command = "help"
    else:
        command = sys.argv[1]
    
    if command == "demo":
        run_demo()
    elif command == "real":
        run_real()
    elif command == "v2":
        test_v2()
    elif command == "quick":
        quick_benchmark()
    elif command in ["help", "--help", "-h"]:
        show_usage()
    else:
        print_error(f"Unknown command: {command}")
        print()
        show_usage()
        sys.exit(1)

if __name__ == "__main__":
    main()
