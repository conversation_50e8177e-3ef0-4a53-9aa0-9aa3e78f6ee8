# UltraFast Modular Indexing System

A high-performance, modular indexing system that supports multiple parallel approaches for ultra-fast data querying and comprehensive performance comparison.

## 🚀 Key Features

- **🔧 Modular Architecture**: Plugin-based system supporting multiple indexing approaches
- **⚡ Multiple Implementations**: V2 (hash-based) and V3 (columnar) with more coming
- **📊 Comprehensive Benchmarking**: Built-in performance comparison framework
- **🎯 Microsecond Performance**: Sub-millisecond query execution across implementations
- **🔍 Advanced Features**: Bloom filters, compression, memory mapping, and more
- **📈 Detailed Analytics**: In-depth performance metrics and reporting

## 🏗️ Architecture

### Core Components

```
interfaces.go          # Common interfaces for all implementations
registry.go           # Implementation registry and management
benchmark.go          # Comprehensive benchmarking framework
metrics.go            # Performance metrics collection
data_generator.go     # Test data generation utilities
```

### Implementation Modules

```
v2_generator.go       # UltraFast V2 index generation
v2_query_engine.go    # UltraFast V2 query execution
v2_common.go          # V2 shared types and utilities

v3_generator.go       # UltraFast V3 index generation  
v3_query_engine.go    # UltraFast V3 query execution
ultrafast_v3.go       # V3 columnar storage implementation
```

### CLI Tools

```
main_new.go           # Main CLI interface
demo.go               # Interactive demonstration
```

## 🔧 Available Implementations

### UltraFast V2
- **Approach**: Hash-based indexing with roaring bitmaps
- **Features**: CRC32C hashing, bloom filters, memory mapping, SIMD optimizations
- **Best for**: Point queries, high-frequency lookups
- **Performance**: ~500μs average query time

### UltraFast V3  
- **Approach**: Columnar storage with adaptive compression
- **Features**: Dictionary encoding, RLE, delta compression, zone maps, block storage
- **Best for**: Analytical queries, compression-heavy workloads
- **Performance**: Optimized for scan operations and compression

## 🛠️ Quick Start

### 1. List Available Implementations

```bash
go run *.go list
```

### 2. Generate Indexes

```bash
# Generate V2 indexes
go run *.go generate ultrafast_v2 mock_data.csv ./indexes demo_table

# Generate V3 indexes  
go run *.go generate ultrafast_v3 mock_data.csv ./indexes demo_table
```

### 3. Query Data

```bash
# Query with V2
go run *.go query ultrafast_v2 ./indexes demo_table "protocol=TCP"

# Query with V3
go run *.go query ultrafast_v3 ./indexes demo_table "action=ALLOW"
```

### 4. Run Performance Comparison

```bash
# Compare all implementations
go run *.go compare mock_data.csv ./comparison demo_table 10000
```

### 5. Run Interactive Demo

```bash
go run *.go demo
```

## 📊 Performance Comparison

The system includes a comprehensive benchmarking framework that measures:

- **Index Generation Time**: How fast indexes are built
- **Index Size**: Storage efficiency and compression ratios  
- **Query Performance**: Average, min, max query times
- **Memory Usage**: Peak memory consumption during operations
- **Throughput**: Queries per second capability

### Sample Comparison Output

```
📈 UltraFast Indexing Performance Comparison Report
======================================================================

📊 Performance Summary:
----------------------------------------------------------------------
Implementation       Build Time   Avg Query    QPS        Size      
----------------------------------------------------------------------
ultrafast_v2         245ms        487μs        2053       1.2MB     
ultrafast_v3         892ms        1.2ms        833        856KB     

🔍 Detailed Results:
======================================================================

ultrafast_v2:
  📁 Index Size: 1.2MB
  ⏱️  Build Time: 245ms
  🔍 Avg Query: 487μs
  ⚡ Min Query: 234μs
  🐌 Max Query: 1.2ms
  🚀 QPS: 2053
  💾 Memory: 15.3MB
  📦 Compression: 67.8%
  🔧 Features: Hash Table Indexing, CRC32C Hashing, Roaring Bitmap Compression, Bloom Filters, Memory Mapping, Key Prefixes, Quadratic Probing, SIMD Optimizations

ultrafast_v3:
  📁 Index Size: 856KB
  ⏱️  Build Time: 892ms
  🔍 Avg Query: 1.2ms
  ⚡ Min Query: 891μs
  🐌 Max Query: 2.1ms
  🚀 QPS: 833
  💾 Memory: 22.7MB
  📦 Compression: 78.2%
  🔧 Features: Columnar Storage, Dictionary Encoding, Run Length Encoding, Delta Encoding, Frame of Reference, Bit Packing, Zone Maps, Bloom Filters, Block-based Storage, Schema Evolution, Multi-level Indexing, Adaptive Compression
```

## 🔍 Command Reference

| Command | Description | Example |
|---------|-------------|---------|
| `generate` | Create indexes for a dataset | `go run *.go generate ultrafast_v2 data.csv ./indexes table` |
| `query` | Execute queries against indexes | `go run *.go query ultrafast_v2 ./indexes table "col=value"` |
| `compare` | Run comprehensive performance comparison | `go run *.go compare data.csv ./output table 10000` |
| `benchmark` | Run benchmarks for specific implementation | `go run *.go benchmark ultrafast_v2 ./indexes queries.txt` |
| `validate` | Validate index file integrity | `go run *.go validate ultrafast_v2 ./indexes/col.ufidx` |
| `stats` | Show index statistics | `go run *.go stats ultrafast_v2 ./indexes column_name` |
| `list` | List all available implementations | `go run *.go list` |

## 🧪 Adding New Implementations

The modular architecture makes it easy to add new indexing approaches:

1. **Implement the interfaces**:
   ```go
   type MyGenerator struct{}
   func (g *MyGenerator) GetApproach() IndexingApproach { return "my_approach" }
   func (g *MyGenerator) GenerateIndex(...) (*IndexStats, error) { /* implementation */ }
   // ... implement other IndexGenerator methods
   
   type MyQueryEngine struct{}
   func (e *MyQueryEngine) GetApproach() IndexingApproach { return "my_approach" }
   func (e *MyQueryEngine) ExecuteQuery(...) (*QueryResult, error) { /* implementation */ }
   // ... implement other QueryEngine methods
   ```

2. **Register the implementation**:
   ```go
   RegisterImplementation(&IndexingImplementation{
       Generator:   NewMyGenerator(""),
       QueryEngine: NewMyQueryEngine(""),
       Name:        "My Custom Implementation",
       Description: "Description of the approach",
       Version:     "1.0",
   })
   ```

3. **Test with the framework**:
   ```bash
   go run *.go compare data.csv ./test my_table 1000
   ```

## 📈 Use Cases

- **Performance Research**: Compare different indexing strategies
- **Production Optimization**: Choose the best approach for your workload
- **Algorithm Development**: Test new indexing techniques
- **Benchmarking**: Standardized performance evaluation
- **Educational**: Learn about different indexing approaches

## 🔬 Technical Details

### V2 Implementation
- **Hash Function**: CRC32C (Castagnoli) with hardware acceleration
- **Collision Resolution**: Quadratic probing with load factor 0.7
- **Compression**: Roaring bitmaps for line number storage
- **Optimization**: Memory mapping, bloom filters, key prefixes

### V3 Implementation  
- **Storage Format**: Columnar with 64KB blocks
- **Compression**: Dictionary, RLE, Delta, FOR encoding
- **Indexing**: Zone maps, bloom filters, sparse indexes
- **Schema**: Self-describing with evolution support

## 📝 License

This project is part of the UltraFast indexing research and development initiative.
