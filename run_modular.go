package main

import (
	"fmt"
	"os"
	"strings"
)

// Entry point for the modular system
func runModularMain() {
	// Check if we should run the modular demo
	if len(os.Args) > 1 {
		switch os.Args[1] {
		case "modular":
			demoWorkingMain()
			return
		case "demo":
			runWorkingDemo()
			return
		case "compare":
			runSimpleComparison()
			return
		case "list":
			listImplementations()
			return
		case "help":
			printModularHelp()
			return
		}
	}

	// Default: show help
	printModularHelp()
}

// listImplementations shows available implementations
func listImplementations() {
	fmt.Println("🔧 Available Indexing Implementations")
	fmt.Println(strings.Repeat("=", 50))

	InitializeRegistry()
	implementations := GetAllImplementations()

	if len(implementations) == 0 {
		fmt.Println("❌ No implementations registered")
		return
	}

	for i, impl := range implementations {
		fmt.Printf("\n%d. %s (v%s)\n", i+1, impl.Name, impl.Version)
		fmt.Printf("   Approach: %s\n", impl.Generator.GetApproach())
		fmt.Printf("   Description: %s\n", impl.Description)
		fmt.Printf("   Features: %s\n", strings.Join(impl.Generator.GetFeatures(), ", "))
		fmt.Printf("   Operators: %s\n", strings.Join(impl.QueryEngine.GetSupportedOperators(), ", "))
	}

	fmt.Printf("\n✅ Found %d implementation(s)\n", len(implementations))
}

// printModularHelp shows usage information
func printModularHelp() {
	fmt.Println("🚀 UltraFast Modular Indexing System")
	fmt.Println(strings.Repeat("=", 50))
	fmt.Println()
	fmt.Println("This is a modular indexing system that supports multiple")
	fmt.Println("parallel approaches for ultra-fast data querying.")
	fmt.Println()
	fmt.Println("Available commands:")
	fmt.Println("  go run run_modular.go demo     - Run interactive demo")
	fmt.Println("  go run run_modular.go compare  - Run performance comparison")
	fmt.Println("  go run run_modular.go list     - List available implementations")
	fmt.Println("  go run run_modular.go help     - Show this help")
	fmt.Println()
	fmt.Println("Examples:")
	fmt.Println("  go run run_modular.go demo")
	fmt.Println("  go run run_modular.go compare")
	fmt.Println()
	fmt.Println("For the original UltraFast V2 system, use:")
	fmt.Println("  go run main.go ultrafast_v2.go [command]")
}
