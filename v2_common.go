package main

import (
	"bytes"
	"hash/crc32"
	"math"
	"unsafe"

	"github.com/RoaringBitmap/roaring"
)

// UltraFastV2 file format constants
const (
	V2_MAGIC_NUMBER    = "UFASTV2\x00" // 8 bytes
	V2_HEADER_SIZE     = 128           // Increased for better alignment
	V2_KEY_PREFIX_SIZE = 8             // Size of the key prefix for quick checks
)

var crc32cTable = crc32.MakeTable(crc32.Castagnoli)

// V2Header represents the optimized file header
type V2Header struct {
	Magic           [8]byte
	Version         uint32
	NumKeys         uint32
	HashTableSize   uint32
	KeyEntrySize    uint32
	DataSectionSize uint64
	BloomFilterSize uint32
	Checksum        uint32
	Reserved        [88]byte // Padding to 128 bytes
}

// CompressedKeyEntry represents a compressed key entry with a prefix for faster collision checks.
type CompressedKeyEntry struct {
	KeyHash    uint32
	KeyPrefix  [V2_KEY_PREFIX_SIZE]byte // Prefix of the key for fast filtering
	DataOffset uint32
	Count      uint32
}

// BloomFilter for fast negative lookups
type BloomFilter struct {
	bits []uint64
	size uint32
	k    uint32
}

// NewBloomFilter creates a new bloom filter
func NewBloomFilter(expectedElements uint32, falsePositiveRate float64) *BloomFilter {
	m := uint32(-float64(expectedElements) * math.Log(falsePositiveRate) / (math.Log(2) * math.Log(2)))
	k := uint32(float64(m) / float64(expectedElements) * math.Log(2))
	if k < 1 {
		k = 1
	}
	m = ((m + 63) / 64) * 64

	return &BloomFilter{
		bits: make([]uint64, m/64),
		size: m,
		k:    k,
	}
}

// Add adds an element to the bloom filter
func (bf *BloomFilter) Add(data string) {
	h1 := crc32cHash(data)
	h2 := fnvHash32(data)
	for i := uint32(0); i < bf.k; i++ {
		hash := (h1 + i*h2) % bf.size
		bf.bits[hash/64] |= 1 << (hash % 64)
	}
}

// Contains checks if an element might be in the set
func (bf *BloomFilter) Contains(data string) bool {
	h1 := crc32cHash(data)
	h2 := fnvHash32(data)
	for i := uint32(0); i < bf.k; i++ {
		hash := (h1 + i*h2) % bf.size
		if (bf.bits[hash/64] & (1 << (hash % 64))) == 0 {
			return false
		}
	}
	return true
}

// Helper functions
func fnvHash32(s string) uint32 {
	h := uint32(2166136261)
	for _, b := range []byte(s) {
		h ^= uint32(b)
		h *= 16777619
	}
	return h
}

func nextPowerOf2(n uint32) uint32 {
	if n == 0 {
		return 1
	}
	n--
	n |= n >> 1
	n |= n >> 2
	n |= n >> 4
	n |= n >> 8
	n |= n >> 16
	n++
	return n
}

// crc32cHash uses hardware-accelerated CRC32c for fast and robust hashing.
func crc32cHash(s string) uint32 {
	return crc32.Checksum([]byte(s), crc32cTable)
}

// prefetchCacheLine hints the CPU to prefetch a cache line.
func prefetchCacheLine(addr unsafe.Pointer) {
	// This is a no-op in pure Go. For maximum performance, this would be implemented in assembly.
	// Example for amd64: PREFETCHT0 (AX)
	_ = addr
}

// compressLineNumbersRoaring compresses line numbers using a roaring bitmap.
func compressLineNumbersRoaring(lineNumbers []uint32) ([]byte, error) {
	rb := roaring.BitmapOf(lineNumbers...)
	rb.RunOptimize() // Optimize for smaller serialized size
	var buf bytes.Buffer
	_, err := rb.WriteTo(&buf)
	return buf.Bytes(), err
}
