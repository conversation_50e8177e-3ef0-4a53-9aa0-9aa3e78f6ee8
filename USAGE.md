# UltraFast V2 Usage Guide

## Quick Commands

### Generate Indexes
```bash
# Generate V2 indexes for all columns in the CSV
go run main.go ultrafast_v2.go generate mock_data.csv ./demo_results/ultrafast_v2 demo_table
```

### Query Examples
```bash
# Protocol queries
go run main.go ultrafast_v2.go query ./demo_results/ultrafast_v2 demo_table "protocol=TCP" protocol
go run main.go ultrafast_v2.go query ./demo_results/ultrafast_v2 demo_table "protocol=UDP" protocol

# User queries  
go run main.go ultrafast_v2.go query ./demo_results/ultrafast_v2 demo_table "source_username=odjordjevicrp"

# Action queries
go run main.go ultrafast_v2.go query ./demo_results/ultrafast_v2 demo_table "action=ALLOW"
go run main.go ultrafast_v2.go query ./demo_results/ultrafast_v2 demo_table "action=DENY"

# Geographic queries
go run main.go ultrafast_v2.go query ./demo_results/ultrafast_v2 demo_table "source_country=United States"
go run main.go ultrafast_v2.go query ./demo_results/ultrafast_v2 demo_table "destination_country=Canada"

# Rule queries
go run main.go ultrafast_v2.go query ./demo_results/ultrafast_v2 demo_table "rule_name=ALLOW_HTTP"
```

### Validation and Stats
```bash
# Validate an index file
go run main.go ultrafast_v2.go validate ./demo_results/ultrafast_v2/protocol_ultrafast_v2.ufidx

# Show statistics for a column
go run main.go ultrafast_v2.go stats ./demo_results/ultrafast_v2 protocol
```

## Performance Testing

### Single Query Performance
```bash
# Test query performance with timing
time go run main.go ultrafast_v2.go query ./demo_results/ultrafast_v2 demo_table "protocol=TCP" protocol
```

### Multiple Query Test
```bash
# Test different queries to compare performance
echo "Testing TCP queries..."
time go run main.go ultrafast_v2.go query ./demo_results/ultrafast_v2 demo_table "protocol=TCP" protocol

echo "Testing UDP queries..."  
time go run main.go ultrafast_v2.go query ./demo_results/ultrafast_v2 demo_table "protocol=UDP" protocol

echo "Testing user queries..."
time go run main.go ultrafast_v2.go query ./demo_results/ultrafast_v2 demo_table "source_username=odjordjevicrp"
```

## Expected Performance

Based on the 33K record mock_data.csv:

- **TCP queries**: ~500µs, ~11,121 results
- **UDP queries**: ~400µs, ~16,902 results  
- **User queries**: ~300µs, ~1-10 results
- **Action queries**: ~450µs, ~20,000+ results

## File Sizes

Generated index files are typically:
- **Small columns** (actions, protocols): 50-100 KB
- **Medium columns** (usernames, IPs): 200-500 KB
- **Large columns** (messages, descriptions): 1-2 MB

## Troubleshooting

### Common Issues

1. **"No such file or directory"**
   - Ensure mock_data.csv exists
   - Check that indexes were generated first

2. **"Invalid magic number"**
   - Index file may be corrupted
   - Regenerate indexes

3. **"Query failed"**
   - Check query syntax: `column=value`
   - Ensure column name exists in CSV

### Debug Commands

```bash
# Check if indexes exist
ls -la ./demo_results/ultrafast_v2/

# Validate specific index
go run main.go ultrafast_v2.go validate ./demo_results/ultrafast_v2/protocol_ultrafast_v2.ufidx

# Show help
go run main.go ultrafast_v2.go
```

## Advanced Usage

### Custom Data
```bash
# Use your own CSV file
go run main.go ultrafast_v2.go generate your_data.csv ./your_indexes your_table

# Query your data
go run main.go ultrafast_v2.go query ./your_indexes your_table "your_column=your_value"
```

### Batch Operations
```bash
# Generate indexes for multiple datasets
for file in *.csv; do
    echo "Processing $file..."
    go run main.go ultrafast_v2.go generate "$file" "./indexes/${file%.csv}" "${file%.csv}"
done
```
