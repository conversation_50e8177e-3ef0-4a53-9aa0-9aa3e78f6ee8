package main

import (
	"bytes"
	"encoding/binary"
	"fmt"
	"io"
	"os"
	"syscall"
	"time"
	"unsafe"

	"github.com/RoaringBitmap/roaring"
)

// V2QueryEngine implements QueryEngine for UltraFast V2 format
type V2QueryEngine struct {
	indexDir string
	mmapData map[string][]byte
}

// NewV2QueryEngine creates a new V2 query engine
func NewV2QueryEngine(indexDir string) *V2QueryEngine {
	return &V2QueryEngine{
		indexDir: indexDir,
		mmapData: make(map[string][]byte),
	}
}

// GetApproach returns the indexing approach identifier
func (e *V2QueryEngine) GetApproach() IndexingApproach {
	return ApproachV2
}

// Initialize prepares the query engine with index directory
func (e *V2QueryEngine) Initialize(indexDir string) error {
	if indexDir != "" {
		e.indexDir = indexDir
	}
	return nil
}

// ExecuteQuery executes a single filter query
func (e *V2QueryEngine) ExecuteQuery(columnName string, filter QueryFilter) (*QueryResult, error) {
	if filter.Operator != "=" {
		return nil, fmt.Errorf("V2 engine only supports equality queries, got: %s", filter.Operator)
	}

	searchValue, ok := filter.Value.(string)
	if !ok {
		return nil, fmt.Errorf("V2 engine only supports string values")
	}

	start := time.Now()

	filename := fmt.Sprintf("%s/%s_ultrafast_v2.ufidx", e.indexDir, columnName)
	data, err := e.getMmapDataV2(filename)
	if err != nil {
		return nil, err
	}

	header := (*V2Header)(unsafe.Pointer(&data[0]))
	if string(header.Magic[:]) != V2_MAGIC_NUMBER {
		return nil, fmt.Errorf("invalid V2 magic number")
	}

	offset := uint64(V2_HEADER_SIZE)

	// 1. Check Bloom Filter (optional optimization)
	bloomFilterData := data[offset : offset+uint64(header.BloomFilterSize)]
	if !e.checkBloomFilter(bloomFilterData, searchValue) {
		// Value definitely not in index
		return &QueryResult{
			LineNumbers:     []uint32{},
			ExecutionTime:   time.Since(start),
			ResultCount:     0,
			BloomFilterHits: 1,
		}, nil
	}

	offset += uint64(header.BloomFilterSize)

	// 2. Search Hash Table with Quadratic Probing
	hashTableOffset := offset
	searchHash := crc32cHash(searchValue)
	initialHash := searchHash % header.HashTableSize
	var probe uint32 = 0

	var searchPrefix [V2_KEY_PREFIX_SIZE]byte
	copy(searchPrefix[:], searchValue)

	for {
		currentPos := (initialHash + probe*probe) % header.HashTableSize
		if probe > header.HashTableSize { // Scanned the whole table
			return &QueryResult{
				LineNumbers:   []uint32{},
				ExecutionTime: time.Since(start),
				ResultCount:   0,
			}, nil
		}

		// Prefetch next cache line for better performance
		prefetchCacheLine(unsafe.Pointer(&data[hashTableOffset+uint64((currentPos+16)%header.HashTableSize)*4]))

		idxOffset := hashTableOffset + uint64(currentPos)*4
		keyIndex := binary.LittleEndian.Uint32(data[idxOffset : idxOffset+4])

		if keyIndex == 0 {
			// Empty slot, value not found
			return &QueryResult{
				LineNumbers:   []uint32{},
				ExecutionTime: time.Since(start),
				ResultCount:   0,
			}, nil
		}

		keyDirOffset := offset + uint64(header.HashTableSize*4) + uint64(keyIndex-1)*uint64(header.KeyEntrySize)
		keyEntry := (*CompressedKeyEntry)(unsafe.Pointer(&data[keyDirOffset]))

		// 3. Quick Hash and Prefix comparison
		if keyEntry.KeyHash == searchHash && keyEntry.KeyPrefix == searchPrefix {
			// 4. Full Key verification
			dataSecOffset := offset + uint64(header.HashTableSize*4) + uint64(header.NumKeys)*uint64(header.KeyEntrySize)
			keyDataOffset := dataSecOffset + uint64(keyEntry.DataOffset)
			keyLength := binary.LittleEndian.Uint16(data[keyDataOffset : keyDataOffset+2])

			if int(keyLength) == len(searchValue) {
				keyData := data[keyDataOffset+2 : keyDataOffset+2+uint64(keyLength)]
				if string(keyData) == searchValue {
					// Found it, decompress line numbers
					compressedDataOffset := keyDataOffset + 2 + uint64(keyLength)
					lineNumbers, err := decompressLineNumbersRoaring(data[compressedDataOffset:])
					if err != nil {
						return nil, err
					}

					return &QueryResult{
						LineNumbers:   lineNumbers,
						ExecutionTime: time.Since(start),
						ResultCount:   len(lineNumbers),
						IndexHits:     1,
					}, nil
				}
			}
		}
		probe++
	}
}

// ExecuteMultiColumnQuery executes a query with multiple filters
func (e *V2QueryEngine) ExecuteMultiColumnQuery(filters []QueryFilter) (*QueryResult, error) {
	if len(filters) == 0 {
		return &QueryResult{}, nil
	}

	start := time.Now()
	var resultLineNumbers []uint32
	var totalIndexHits int

	// Execute first query
	firstResult, err := e.ExecuteQuery(filters[0].Column, filters[0])
	if err != nil {
		return nil, err
	}

	resultLineNumbers = firstResult.LineNumbers
	totalIndexHits += firstResult.IndexHits

	// Intersect with remaining queries
	for i := 1; i < len(filters); i++ {
		if len(resultLineNumbers) == 0 {
			break // No point continuing if no results
		}

		queryResult, err := e.ExecuteQuery(filters[i].Column, filters[i])
		if err != nil {
			return nil, err
		}

		totalIndexHits += queryResult.IndexHits

		// Intersect line numbers
		resultLineNumbers = e.intersectLineNumbers(resultLineNumbers, queryResult.LineNumbers)
	}

	return &QueryResult{
		LineNumbers:   resultLineNumbers,
		ExecutionTime: time.Since(start),
		ResultCount:   len(resultLineNumbers),
		IndexHits:     totalIndexHits,
	}, nil
}

// ExecuteBatchQueries executes multiple queries and returns aggregated results
func (e *V2QueryEngine) ExecuteBatchQueries(queries []QueryFilter) ([]*QueryResult, error) {
	results := make([]*QueryResult, len(queries))

	for i, query := range queries {
		result, err := e.ExecuteQuery(query.Column, query)
		if err != nil {
			return nil, fmt.Errorf("batch query %d failed: %v", i, err)
		}
		results[i] = result
	}

	return results, nil
}

// GetSupportedOperators returns the list of supported query operators
func (e *V2QueryEngine) GetSupportedOperators() []string {
	return []string{"="}
}

// Close releases any resources held by the query engine
func (e *V2QueryEngine) Close() error {
	for filename, data := range e.mmapData {
		if err := syscall.Munmap(data); err != nil {
			return fmt.Errorf("failed to unmap %s: %v", filename, err)
		}
	}
	e.mmapData = make(map[string][]byte)
	return nil
}

// Helper methods

// getMmapDataV2 gets memory-mapped data for V2 format
func (e *V2QueryEngine) getMmapDataV2(filename string) ([]byte, error) {
	if data, exists := e.mmapData[filename]; exists {
		return data, nil
	}

	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	stat, err := file.Stat()
	if err != nil {
		return nil, err
	}

	data, err := syscall.Mmap(int(file.Fd()), 0, int(stat.Size()),
		syscall.PROT_READ, syscall.MAP_SHARED)
	if err != nil {
		return nil, err
	}

	e.mmapData[filename] = data
	return data, nil
}

// checkBloomFilter checks if a value might be in the bloom filter
func (e *V2QueryEngine) checkBloomFilter(bloomData []byte, value string) bool {
	// Simplified bloom filter check
	// In a real implementation, this would reconstruct the bloom filter
	// and check membership properly
	return true // For now, always return true (no false negatives)
}

// intersectLineNumbers finds the intersection of two sorted line number arrays
func (e *V2QueryEngine) intersectLineNumbers(a, b []uint32) []uint32 {
	result := make([]uint32, 0, min(len(a), len(b)))
	i, j := 0, 0

	for i < len(a) && j < len(b) {
		if a[i] == b[j] {
			result = append(result, a[i])
			i++
			j++
		} else if a[i] < b[j] {
			i++
		} else {
			j++
		}
	}

	return result
}

// min returns the minimum of two integers
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// decompressLineNumbersRoaring decompresses roaring-bitmap-encoded line numbers
func decompressLineNumbersRoaring(data []byte) ([]uint32, error) {
	rb := roaring.New()
	// Use ReadFrom to deserialize the bitmap
	if _, err := rb.ReadFrom(bytes.NewReader(data)); err != nil && err != io.EOF {
		return nil, err
	}
	return rb.ToArray(), nil
}
