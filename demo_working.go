package main

import (
	"fmt"
	"os"
	"strings"
	"time"
)

// WorkingDemo demonstrates the modular system with existing code
func runWorkingDemo() {
	fmt.Println("🚀 UltraFast Modular System Demo (Working Version)")
	fmt.Println(strings.Repeat("=", 60))
	fmt.Println()

	// Initialize registry
	InitializeRegistry()

	// Show available implementations
	fmt.Println("📦 Available Implementations:")
	implementations := GetAllImplementations()
	for i, impl := range implementations {
		fmt.Printf("  %d. %s - %s\n", i+1, impl.Name, impl.Description)
		fmt.Printf("     Features: %s\n", strings.Join(impl.Generator.GetFeatures(), ", "))
		fmt.Printf("     Operators: %s\n", strings.Join(impl.QueryEngine.GetSupportedOperators(), ", "))
	}
	fmt.Println()

	// Generate test data using existing data generator
	fmt.Println("📊 Generating test data...")
	generator := NewTestDataGenerator()
	
	schema := map[string]string{
		"protocol": "string",
		"action":   "string",
		"port":     "int",
	}
	
	dataset, err := generator.GenerateRealisticData(1000, schema)
	if err != nil {
		fmt.Printf("Error generating data: %v\n", err)
		return
	}
	
	fmt.Printf("✅ Generated %d records with %d columns\n", dataset.Size, len(dataset.ColumnData))
	
	// Show sample data
	for columnName, records := range dataset.ColumnData {
		fmt.Printf("   %s: %d records (sample: %s)\n", 
			columnName, len(records), 
			getSampleValues(records, 3))
	}
	fmt.Println()

	// Test V2 implementation
	fmt.Println("🔧 Testing UltraFast V2...")
	fmt.Println(strings.Repeat("-", 40))
	
	v2Impl, err := GetImplementation(ApproachV2)
	if err != nil {
		fmt.Printf("❌ Error getting V2 implementation: %v\n", err)
		return
	}
	
	outputDir := "./demo_working_temp"
	os.RemoveAll(outputDir)
	defer os.RemoveAll(outputDir)
	
	// Test index generation
	fmt.Printf("  📝 Generating indexes...")
	start := time.Now()
	stats, err := v2Impl.Generator.GenerateMultiColumnIndex(dataset.ColumnData, outputDir, "demo_table")
	if err != nil {
		fmt.Printf(" ❌ Failed: %v\n", err)
		return
	}
	genTime := time.Since(start)
	fmt.Printf(" ✅ %v\n", genTime)
	
	// Show generation stats
	totalSize := int64(0)
	for columnName, stat := range stats {
		totalSize += stat.FileSize
		fmt.Printf("    %s: %d records, %s, %.1f%% compression\n",
			columnName, stat.RecordCount, formatBytesHelper(stat.FileSize), stat.CompressionRatio*100)
	}
	fmt.Printf("  📁 Total index size: %s\n", formatBytesHelper(totalSize))
	
	// Test query engine
	fmt.Printf("  🔍 Testing queries...")
	if err := v2Impl.QueryEngine.Initialize(outputDir); err != nil {
		fmt.Printf(" ❌ Failed to initialize: %v\n", err)
		return
	}
	defer v2Impl.QueryEngine.Close()
	
	// Run test queries
	queryCount := 0
	totalQueryTime := time.Duration(0)
	
	for _, query := range dataset.QuerySet {
		if queryCount >= 3 { // Limit to 3 queries for demo
			break
		}
		
		start := time.Now()
		result, err := v2Impl.QueryEngine.ExecuteQuery(query.Column, query)
		queryTime := time.Since(start)
		
		if err != nil {
			fmt.Printf("\n    ❌ Query failed: %v", err)
			continue
		}
		
		totalQueryTime += queryTime
		queryCount++
		
		if queryCount == 1 {
			fmt.Printf(" %v", queryTime)
		}
		
		fmt.Printf("\n    Query: %s=%v -> %d results", query.Column, query.Value, result.ResultCount)
	}
	
	if queryCount > 1 {
		avgTime := totalQueryTime / time.Duration(queryCount)
		fmt.Printf("\n  ⚡ Average query time: %v", avgTime)
	}
	fmt.Println()
	
	// Test validation
	fmt.Printf("  ✅ Validating indexes...")
	validationCount := 0
	for columnName := range dataset.ColumnData {
		indexFile := fmt.Sprintf("%s/%s_ultrafast_v2.ufidx", outputDir, columnName)
		if err := v2Impl.Generator.ValidateIndex(indexFile); err != nil {
			fmt.Printf(" ❌ Validation failed for %s: %v\n", columnName, err)
			return
		}
		validationCount++
		
		if validationCount >= 2 { // Limit validation tests
			break
		}
	}
	fmt.Printf(" ✅ %d files validated\n", validationCount)
	
	fmt.Println()
	fmt.Println("🎉 Demo completed successfully!")
	fmt.Println()
	fmt.Println("💡 Next steps:")
	fmt.Println("   - Add more implementations to the registry")
	fmt.Println("   - Run performance comparisons")
	fmt.Println("   - Test with your own data")
}

// getSampleValues returns a sample of values from records
func getSampleValues(records []Record, count int) string {
	if len(records) == 0 {
		return "no data"
	}
	
	samples := make([]string, 0, count)
	step := len(records) / count
	if step == 0 {
		step = 1
	}
	
	for i := 0; i < len(records) && len(samples) < count; i += step {
		samples = append(samples, records[i].Value)
	}
	
	return strings.Join(samples, ", ")
}

// formatBytesHelper formats bytes for display
func formatBytesHelper(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}

// Simple comparison demo
func runSimpleComparison() {
	fmt.Println("📊 Simple Performance Comparison")
	fmt.Println(strings.Repeat("=", 40))
	fmt.Println()

	// Initialize registry
	InitializeRegistry()

	// Generate test data
	generator := NewTestDataGenerator()
	schema := map[string]string{
		"protocol": "string",
		"action":   "string",
	}
	
	dataset, err := generator.GenerateRealisticData(500, schema)
	if err != nil {
		fmt.Printf("Error generating data: %v\n", err)
		return
	}
	
	fmt.Printf("📊 Testing with %d records\n", dataset.Size)
	fmt.Println()

	// Test each implementation
	implementations := GetAllImplementations()
	for _, impl := range implementations {
		fmt.Printf("🔧 Testing %s...\n", impl.Name)
		
		outputDir := fmt.Sprintf("./comparison_temp/%s", impl.Generator.GetApproach())
		os.RemoveAll(outputDir)
		defer os.RemoveAll(outputDir)
		
		// Measure index generation
		start := time.Now()
		stats, err := impl.Generator.GenerateMultiColumnIndex(dataset.ColumnData, outputDir, "comparison_test")
		genTime := time.Since(start)
		
		if err != nil {
			fmt.Printf("  ❌ Generation failed: %v\n", err)
			continue
		}
		
		// Calculate total size
		totalSize := int64(0)
		for _, stat := range stats {
			totalSize += stat.FileSize
		}
		
		// Test query performance
		if err := impl.QueryEngine.Initialize(outputDir); err != nil {
			fmt.Printf("  ❌ Query engine failed: %v\n", err)
			continue
		}
		
		var avgQueryTime time.Duration
		if len(dataset.QuerySet) > 0 {
			start = time.Now()
			_, err := impl.QueryEngine.ExecuteQuery(dataset.QuerySet[0].Column, dataset.QuerySet[0])
			avgQueryTime = time.Since(start)
			if err != nil {
				fmt.Printf("  ❌ Query failed: %v\n", err)
				impl.QueryEngine.Close()
				continue
			}
		}
		
		impl.QueryEngine.Close()
		
		// Display results
		fmt.Printf("  ✅ Generation: %v\n", genTime)
		fmt.Printf("  📁 Index size: %s\n", formatBytesHelper(totalSize))
		if avgQueryTime > 0 {
			fmt.Printf("  🔍 Query time: %v\n", avgQueryTime)
		}
		fmt.Printf("  🔧 Features: %d\n", len(impl.Generator.GetFeatures()))
		fmt.Println()
	}
	
	fmt.Println("✅ Comparison completed!")
}

// Main entry point for working demo
func demoWorkingMain() {
	if len(os.Args) > 1 {
		switch os.Args[1] {
		case "demo":
			runWorkingDemo()
		case "compare":
			runSimpleComparison()
		case "test":
			runWorkingDemo()
		default:
			fmt.Println("Available commands:")
			fmt.Println("  go run *.go demo     - Run full demo")
			fmt.Println("  go run *.go compare  - Run simple comparison")
			fmt.Println("  go run *.go test     - Run test demo")
		}
	} else {
		fmt.Println("🚀 UltraFast Modular Indexing System")
		fmt.Println()
		fmt.Println("Available commands:")
		fmt.Println("  go run *.go demo     - Run full demo")
		fmt.Println("  go run *.go compare  - Run simple comparison")
		fmt.Println("  go run *.go test     - Run test demo")
		fmt.Println()
		fmt.Println("Example: go run *.go demo")
	}
}
