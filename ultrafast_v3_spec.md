# UltraFast V3 File Format Specification

## Overview
UltraFast V3 is an industry-leading columnar storage format designed for microsecond-level query performance, combining the best techniques from BigQuery Capacitor, ClickHouse MergeTree, and modern columnar storage research.

## Key Innovations

### 1. Adaptive Hybrid Storage Architecture
- **True Columnar Storage**: Each column stored separately with optimal encoding
- **Block-Based Organization**: 64KB blocks for optimal I/O and parallel processing
- **Adaptive Compression**: Automatic selection of best compression per column/block
- **Intelligent Row Reordering**: Maximize compression through optimal data arrangement

### 2. Advanced Compression Techniques
- **Dictionary Encoding**: For low-cardinality strings (< 1000 unique values)
- **Run Length Encoding (RLE)**: For repeated values
- **Bit-Packing**: For small integer ranges
- **Frame of Reference (FOR)**: For numeric columns with small deltas
- **Delta Encoding**: For timestamps and sequential data
- **LZ4/ZSTD**: For general-purpose compression
- **Cascaded Encoding**: Multiple compression layers for maximum efficiency

### 3. Multi-Level Indexing System
- **Zone Maps**: Min/max values per block for range queries
- **Bitmap Indexes**: For categorical columns (< 10K cardinality)
- **Bloom Filters**: For existence checks
- **Sparse Indexes**: For high-cardinality columns
- **Inverted Indexes**: For text search capabilities

## File Structure

```
UltraFast V3 File Layout:
┌─────────────────────────────────────────────────────────────┐
│ File Header (256 bytes)                                     │
├─────────────────────────────────────────────────────────────┤
│ Schema Metadata                                             │
├─────────────────────────────────────────────────────────────┤
│ Column Metadata Array                                       │
├─────────────────────────────────────────────────────────────┤
│ Block Index (Zone Maps + Statistics)                       │
├─────────────────────────────────────────────────────────────┤
│ Bloom Filter Section                                        │
├─────────────────────────────────────────────────────────────┤
│ Dictionary Section                                          │
├─────────────────────────────────────────────────────────────┤
│ Column Data Blocks                                          │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Block Header (64 bytes)                                 │ │
│ │ ┌─────────────────────────────────────────────────────┐ │ │
│ │ │ Compressed Column Data                              │ │ │
│ │ │ - Encoding Type                                     │ │ │
│ │ │ - Compression Algorithm                             │ │ │
│ │ │ - Data Payload                                      │ │ │
│ │ └─────────────────────────────────────────────────────┘ │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## Data Structures

### File Header (256 bytes)
```go
type V3Header struct {
    Magic           [8]byte   // "UFASTV3\0"
    Version         uint32    // Format version
    SchemaHash      [32]byte  // SHA-256 of schema
    NumRows         uint64    // Total number of rows
    NumColumns      uint32    // Number of columns
    NumBlocks       uint32    // Number of data blocks
    BlockSize       uint32    // Standard block size (64KB)
    CompressionRatio float32  // Overall compression ratio
    SchemaOffset    uint64    // Offset to schema metadata
    ColumnMetaOffset uint64   // Offset to column metadata
    BlockIndexOffset uint64   // Offset to block index
    BloomFilterOffset uint64  // Offset to bloom filters
    DictionaryOffset uint64   // Offset to dictionaries
    DataOffset      uint64    // Offset to data blocks
    Checksum        uint64    // CRC64 checksum
    Reserved        [144]byte // Future extensions
}
```

### Column Metadata
```go
type ColumnMetadata struct {
    Name            [64]byte  // Column name
    DataType        uint8     // Data type enum
    Encoding        uint8     // Primary encoding type
    Compression     uint8     // Compression algorithm
    Cardinality     uint64    // Unique value count
    NullCount       uint64    // Number of null values
    MinValue        [32]byte  // Minimum value (typed)
    MaxValue        [32]byte  // Maximum value (typed)
    DictionaryID    uint32    // Dictionary ID (if applicable)
    BloomFilterID   uint32    // Bloom filter ID
    IndexType       uint8     // Index type (bitmap, sparse, etc.)
    Reserved        [31]byte  // Future extensions
}
```

### Block Header (64 bytes)
```go
type BlockHeader struct {
    BlockID         uint32    // Block identifier
    RowCount        uint32    // Rows in this block
    CompressedSize  uint32    // Compressed data size
    UncompressedSize uint32   // Original data size
    Encoding        uint8     // Encoding used
    Compression     uint8     // Compression algorithm
    NullCount       uint32    // Null values in block
    MinValue        [16]byte  // Block minimum
    MaxValue        [16]byte  // Block maximum
    Checksum        uint32    // Block checksum
    Reserved        [14]byte  // Future use
}
```

## Encoding Strategies

### 1. Dictionary Encoding
- **Trigger**: Cardinality < 1000 OR compression ratio > 50%
- **Implementation**: Global dictionary with 32-bit IDs
- **Optimization**: Frequency-based ordering for better compression

### 2. Run Length Encoding (RLE)
- **Trigger**: Consecutive identical values > 3
- **Implementation**: (value, count) pairs with variable-length encoding
- **Optimization**: Combined with dictionary encoding

### 3. Bit-Packing
- **Trigger**: Integer values fit in < 32 bits
- **Implementation**: Pack multiple values per word
- **Optimization**: Adaptive bit width per block

### 4. Frame of Reference (FOR)
- **Trigger**: Numeric data with small range
- **Implementation**: Store base + small deltas
- **Optimization**: Optimal base selection per block

### 5. Delta Encoding
- **Trigger**: Monotonic sequences (timestamps, IDs)
- **Implementation**: First value + deltas
- **Optimization**: Variable-length delta encoding

## Query Optimization Features

### 1. Predicate Pushdown
- Zone map filtering before decompression
- Bloom filter existence checks
- Dictionary-based filtering

### 2. Column Pruning
- Read only required columns
- Skip entire column files when possible

### 3. Block Skipping
- Use zone maps to skip irrelevant blocks
- Parallel block processing

### 4. Vectorized Operations
- SIMD-optimized decompression
- Batch processing for better CPU utilization

## Performance Targets

### Storage Efficiency
- **Compression Ratio**: 80-90% reduction vs raw data
- **Space Overhead**: < 5% for metadata and indexes

### Query Performance
- **Point Queries**: < 10 microseconds
- **Range Queries**: < 100 microseconds for 1M rows
- **Aggregations**: < 1 millisecond for 10M rows
- **Full Scans**: > 10GB/s throughput

### Scalability
- **File Size**: Up to 1TB per file
- **Row Count**: Up to 1 billion rows
- **Columns**: Up to 10,000 columns
- **Concurrent Queries**: 1000+ simultaneous readers

## Implementation Phases

1. **Phase 1**: Core columnar storage with basic compression
2. **Phase 2**: Advanced compression and encoding techniques
3. **Phase 3**: Multi-level indexing and zone maps
4. **Phase 4**: Query engine optimizations
5. **Phase 5**: Row reordering and adaptive algorithms

## Compatibility

### Backward Compatibility
- V3 readers can detect and reject V1/V2 files gracefully
- Migration tools provided for V2 → V3 conversion

### Forward Compatibility
- Extensible header with reserved fields
- Version-aware readers for future enhancements
