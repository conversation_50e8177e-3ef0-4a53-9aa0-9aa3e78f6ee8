package main

import (
	"fmt"
	"os"
	"runtime"
	"sort"
	"strings"
	"time"
)

// ComprehensiveBenchmark implements the PerformanceBenchmark interface
type ComprehensiveBenchmark struct {
	metricsCollector MetricsCollector
	tempDir          string
}

// NewComprehensiveBenchmark creates a new benchmark instance
func NewComprehensiveBenchmark() *ComprehensiveBenchmark {
	return &ComprehensiveBenchmark{
		metricsCollector: NewSystemMetricsCollector(),
		tempDir:          "./benchmark_temp",
	}
}

// RunBenchmark executes a comprehensive benchmark for an implementation
func (cb *ComprehensiveBenchmark) RunBenchmark(impl *IndexingImplementation, testData TestDataset) (*BenchmarkResult, error) {
	result := &BenchmarkResult{
		Approach:    impl.Generator.GetApproach(),
		DatasetSize: testData.Size,
		Features:    impl.Generator.GetFeatures(),
		Errors:      make([]string, 0),
	}

	// Ensure temp directory exists
	os.MkdirAll(cb.tempDir, 0755)
	defer os.RemoveAll(cb.tempDir)

	// Start metrics collection
	cb.metricsCollector.StartCollection()

	// Benchmark index generation
	indexBuildStart := time.Now()
	indexStats, err := cb.benchmarkIndexGeneration(impl, testData)
	if err != nil {
		result.Errors = append(result.Errors, fmt.Sprintf("Index generation failed: %v", err))
		return result, err
	}
	result.IndexBuildTime = time.Since(indexBuildStart)
	result.IndexSize = indexStats["total_size"].(int64)

	// Benchmark query performance
	queryResults, err := cb.benchmarkQueryPerformance(impl, testData)
	if err != nil {
		result.Errors = append(result.Errors, fmt.Sprintf("Query benchmark failed: %v", err))
	} else {
		cb.calculateQueryStats(result, queryResults)
	}

	// Collect final metrics
	metrics, _ := cb.metricsCollector.StopCollection()
	result.MemoryUsage = metrics["peak_memory"].(int64)
	result.CompressionRatio = cb.calculateCompressionRatio(testData, result.IndexSize)

	return result, nil
}

// RunComparison runs benchmarks for multiple implementations
func (cb *ComprehensiveBenchmark) RunComparison(implementations []*IndexingImplementation, testData TestDataset) ([]*BenchmarkResult, error) {
	results := make([]*BenchmarkResult, 0, len(implementations))

	fmt.Printf("🚀 Running comprehensive benchmark with %d implementations\n", len(implementations))
	fmt.Printf("📊 Dataset: %s (%d records)\n", testData.Name, testData.Size)
	fmt.Println(strings.Repeat("=", 70))

	for i, impl := range implementations {
		fmt.Printf("\n[%d/%d] Testing %s...\n", i+1, len(implementations), impl.Name)
		
		result, err := cb.RunBenchmark(impl, testData)
		if err != nil {
			fmt.Printf("❌ Failed: %v\n", err)
			result.Errors = append(result.Errors, err.Error())
		} else {
			fmt.Printf("✅ Completed in %v\n", result.IndexBuildTime)
		}
		
		results = append(results, result)
		
		// Force garbage collection between tests
		runtime.GC()
		time.Sleep(100 * time.Millisecond)
	}

	return results, nil
}

// benchmarkIndexGeneration tests index creation performance
func (cb *ComprehensiveBenchmark) benchmarkIndexGeneration(impl *IndexingImplementation, testData TestDataset) (map[string]interface{}, error) {
	outputDir := fmt.Sprintf("%s/%s", cb.tempDir, impl.Generator.GetApproach())
	os.MkdirAll(outputDir, 0755)

	stats := make(map[string]interface{})
	var totalSize int64

	// Generate indexes for each column
	for columnName, records := range testData.ColumnData {
		indexStats, err := impl.Generator.GenerateIndex(columnName, records, outputDir)
		if err != nil {
			return nil, fmt.Errorf("failed to generate index for column %s: %v", columnName, err)
		}
		totalSize += indexStats.FileSize
	}

	stats["total_size"] = totalSize
	stats["columns"] = len(testData.ColumnData)
	
	return stats, nil
}

// benchmarkQueryPerformance tests query execution performance
func (cb *ComprehensiveBenchmark) benchmarkQueryPerformance(impl *IndexingImplementation, testData TestDataset) ([]*QueryResult, error) {
	outputDir := fmt.Sprintf("%s/%s", cb.tempDir, impl.Generator.GetApproach())
	
	// Initialize query engine
	err := impl.QueryEngine.Initialize(outputDir)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize query engine: %v", err)
	}
	defer impl.QueryEngine.Close()

	results := make([]*QueryResult, 0)

	// Run each query multiple times for statistical significance
	for _, query := range testData.QuerySet {
		for i := 0; i < 10; i++ { // Run each query 10 times
			result, err := impl.QueryEngine.ExecuteQuery(query.Column, query)
			if err != nil {
				return nil, fmt.Errorf("query failed: %v", err)
			}
			results = append(results, result)
		}
	}

	return results, nil
}

// calculateQueryStats computes query performance statistics
func (cb *ComprehensiveBenchmark) calculateQueryStats(result *BenchmarkResult, queryResults []*QueryResult) {
	if len(queryResults) == 0 {
		return
	}

	times := make([]time.Duration, len(queryResults))
	for i, qr := range queryResults {
		times[i] = qr.ExecutionTime
	}

	sort.Slice(times, func(i, j int) bool {
		return times[i] < times[j]
	})

	result.MinQueryTime = times[0]
	result.MaxQueryTime = times[len(times)-1]
	
	// Calculate average
	var total time.Duration
	for _, t := range times {
		total += t
	}
	result.AvgQueryTime = total / time.Duration(len(times))
	
	// Calculate queries per second
	avgSeconds := result.AvgQueryTime.Seconds()
	if avgSeconds > 0 {
		result.QueriesPerSecond = 1.0 / avgSeconds
	}
}

// calculateCompressionRatio estimates compression ratio
func (cb *ComprehensiveBenchmark) calculateCompressionRatio(testData TestDataset, indexSize int64) float64 {
	// Estimate raw data size (rough calculation)
	estimatedRawSize := int64(testData.Size * len(testData.ColumnData) * 50) // 50 bytes per field average
	
	if estimatedRawSize > 0 {
		return 1.0 - float64(indexSize)/float64(estimatedRawSize)
	}
	
	return 0.0
}

// GenerateReport creates a detailed performance report
func (cb *ComprehensiveBenchmark) GenerateReport(results []*BenchmarkResult) (string, error) {
	var report strings.Builder
	
	report.WriteString("📈 UltraFast Indexing Performance Comparison Report\n")
	report.WriteString(strings.Repeat("=", 70) + "\n\n")
	
	// Summary table
	report.WriteString("📊 Performance Summary:\n")
	report.WriteString(strings.Repeat("-", 70) + "\n")
	report.WriteString(fmt.Sprintf("%-20s %-12s %-12s %-12s %-10s\n", 
		"Implementation", "Build Time", "Avg Query", "QPS", "Size"))
	report.WriteString(strings.Repeat("-", 70) + "\n")
	
	for _, result := range results {
		if len(result.Errors) == 0 {
			report.WriteString(fmt.Sprintf("%-20s %-12v %-12v %-10.0f %-10s\n",
				string(result.Approach),
				result.IndexBuildTime.Round(time.Millisecond),
				result.AvgQueryTime.Round(time.Microsecond),
				result.QueriesPerSecond,
				formatBytes(result.IndexSize)))
		} else {
			report.WriteString(fmt.Sprintf("%-20s %-12s %-12s %-10s %-10s\n",
				string(result.Approach), "FAILED", "FAILED", "FAILED", "FAILED"))
		}
	}
	
	// Detailed results
	report.WriteString("\n🔍 Detailed Results:\n")
	report.WriteString(strings.Repeat("=", 70) + "\n")
	
	for _, result := range results {
		report.WriteString(fmt.Sprintf("\n%s:\n", string(result.Approach)))
		if len(result.Errors) > 0 {
			report.WriteString("  ❌ Errors:\n")
			for _, err := range result.Errors {
				report.WriteString(fmt.Sprintf("    - %s\n", err))
			}
		} else {
			report.WriteString(fmt.Sprintf("  📁 Index Size: %s\n", formatBytes(result.IndexSize)))
			report.WriteString(fmt.Sprintf("  ⏱️  Build Time: %v\n", result.IndexBuildTime.Round(time.Millisecond)))
			report.WriteString(fmt.Sprintf("  🔍 Avg Query: %v\n", result.AvgQueryTime.Round(time.Microsecond)))
			report.WriteString(fmt.Sprintf("  ⚡ Min Query: %v\n", result.MinQueryTime.Round(time.Microsecond)))
			report.WriteString(fmt.Sprintf("  🐌 Max Query: %v\n", result.MaxQueryTime.Round(time.Microsecond)))
			report.WriteString(fmt.Sprintf("  🚀 QPS: %.0f\n", result.QueriesPerSecond))
			report.WriteString(fmt.Sprintf("  💾 Memory: %s\n", formatBytes(result.MemoryUsage)))
			report.WriteString(fmt.Sprintf("  📦 Compression: %.1f%%\n", result.CompressionRatio*100))
			report.WriteString(fmt.Sprintf("  🔧 Features: %s\n", strings.Join(result.Features, ", ")))
		}
	}
	
	return report.String(), nil
}

// Helper function to format bytes
func formatBytes(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}
