#!/bin/bash

# Run tests and demos for the UltraFast Modular Indexing System

echo "🚀 UltraFast Modular Indexing System Tests"
echo "=========================================="
echo

# Function to run a test with proper formatting
run_test() {
  echo "🧪 Running $1..."
  echo "----------------------------------------"
  go run *.go $2
  echo "----------------------------------------"
  echo "✅ $1 completed"
  echo
}

# Basic functionality test
run_test "Basic Functionality Test" "basic"

# Full modular system test
run_test "Full Modular System Test" "test"

# Test with real data if available
if [ -f "mock_data.csv" ]; then
  run_test "Real Data Test" "real"
else
  echo "⚠️  mock_data.csv not found, skipping real data test"
  echo
fi

# Run the demo
echo "🎮 Running Interactive Demo..."
echo "----------------------------------------"
go run *.go demo
echo "----------------------------------------"
echo "✅ Demo completed"
echo

echo "🎉 All tests and demos completed successfully!"
